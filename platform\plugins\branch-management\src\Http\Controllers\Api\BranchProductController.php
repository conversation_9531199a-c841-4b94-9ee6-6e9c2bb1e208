<?php

namespace Botble\BranchManagement\Http\Controllers\Api;

use Botble\Base\Http\Controllers\BaseController;
use Botble\BranchManagement\Repositories\Interfaces\BranchInterface;
use Bo<PERSON>ble\Ecommerce\Models\BranchInventory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BranchProductController extends BaseController
{
    public function __construct(protected BranchInterface $branchRepository)
    {
    }

    /**
     * Get branches by city with product availability
     */
    public function getBranchesByCity(Request $request): JsonResponse
    {
        $request->validate([
            'city_id' => 'required|integer|exists:cities,id',
            'product_id' => 'nullable|integer|exists:ec_products,id',
        ]);

        $cityId = $request->input('city_id');
        $productId = $request->input('product_id');

        $branches = $this->branchRepository->getBranchesByCity($cityId, true);

        // If product_id is provided, filter branches by stock availability
        if ($productId) {
            $branches = $branches->filter(function ($branch) use ($productId) {
                $inventory = BranchInventory::where('branch_id', $branch->id)
                    ->where('product_id', $productId)
                    ->first();

                return $inventory && $inventory->quantity > 0;
            });
        }

        $branchData = $branches->map(function ($branch) use ($productId) {
            $data = [
                'id' => $branch->id,
                'name' => $branch->name,
                'address' => $branch->address,
                'full_address' => $branch->full_address,
                'phone' => $branch->phone,
                'email' => $branch->email,
                'operating_hours' => $branch->operating_hours,
                'is_pickup_available' => $branch->is_pickup_available,
                'city' => [
                    'id' => $branch->city->id,
                    'name' => $branch->city->name,
                ],
            ];

            // Add stock information if product_id is provided
            if ($productId) {
                $inventory = BranchInventory::where('branch_id', $branch->id)
                    ->where('product_id', $productId)
                    ->first();

                $data['stock'] = [
                    'quantity' => $inventory ? $inventory->quantity : 0,
                    'is_in_stock' => $inventory ? $inventory->isInStock() : false,
                    'is_low_stock' => $inventory ? $inventory->isLowStock() : false,
                ];
            }

            return $data;
        });

        return $this->httpResponse()->setData($branchData->values());
    }

    /**
     * Get product stock across all branches
     */
    public function getProductStock(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|integer|exists:ec_products,id',
        ]);

        $productId = $request->input('product_id');

        $inventory = BranchInventory::with(['branch.city'])
            ->where('product_id', $productId)
            ->get();

        $stockData = $inventory->map(function ($item) {
            return [
                'branch_id' => $item->branch_id,
                'branch_name' => $item->branch->name,
                'city_name' => $item->branch->city->name ?? 'Unknown',
                'quantity' => $item->quantity,
                'is_in_stock' => $item->isInStock(),
                'is_low_stock' => $item->isLowStock(),
                'is_out_of_stock' => $item->isOutOfStock(),
            ];
        });

        return $this->httpResponse()->setData($stockData);
    }

    /**
     * Check product availability at specific branch
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'required|integer|exists:branches,id',
            'product_id' => 'required|integer|exists:ec_products,id',
            'quantity' => 'integer|min:1',
        ]);

        $branchId = $request->input('branch_id');
        $productId = $request->input('product_id');
        $requiredQuantity = $request->input('quantity', 1);

        $inventory = BranchInventory::where('branch_id', $branchId)
            ->where('product_id', $productId)
            ->first();

        if (!$inventory) {
            return $this->httpResponse()->setData([
                'available' => false,
                'quantity' => 0,
                'sufficient_stock' => false,
                'message' => 'Product not available at this branch',
            ]);
        }

        return $this->httpResponse()->setData([
            'available' => $inventory->isInStock(),
            'quantity' => $inventory->quantity,
            'sufficient_stock' => $inventory->hasSufficientStock($requiredQuantity),
            'is_low_stock' => $inventory->isLowStock(),
            'message' => $inventory->isInStock() 
                ? ($inventory->hasSufficientStock($requiredQuantity) ? 'Product available' : 'Insufficient stock')
                : 'Product out of stock',
        ]);
    }
}
