{"__meta": {"id": "01K433VS54PTR9MMX0A5TQNWMP", "datetime": "2025-09-01 17:05:43", "utime": **********.589486, "method": "POST", "uri": "/admin/ecommerce/product-inventory?", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 514, "start": 1756746341.343746, "end": **********.589502, "duration": 2.245756149291992, "duration_str": "2.25s", "measures": [{"label": "Booting", "start": 1756746341.343746, "relative_start": 0, "end": **********.267039, "relative_end": **********.267039, "duration": 0.****************, "duration_str": "923ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.267052, "relative_start": 0.****************, "end": **********.589504, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.286804, "relative_start": 0.****************, "end": **********.29661, "relative_end": **********.29661, "duration": 0.009806156158447266, "duration_str": "9.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::table-info", "start": **********.362002, "relative_start": 1.****************, "end": **********.362002, "relative_end": **********.362002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.367523, "relative_start": 1.****************, "end": **********.367523, "relative_end": **********.367523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::badge", "start": **********.369809, "relative_start": 1.0260629653930664, "end": **********.369809, "relative_end": **********.369809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.486414, "relative_start": 1.1426680088043213, "end": **********.486414, "relative_end": **********.486414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.557455, "relative_start": 1.2137091159820557, "end": **********.557455, "relative_end": **********.557455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.783413, "relative_start": 1.439666986465454, "end": **********.783413, "relative_end": **********.783413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.045032, "relative_start": 1.7012860774993896, "end": **********.045032, "relative_end": **********.045032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.045399, "relative_start": 1.701653003692627, "end": **********.045399, "relative_end": **********.045399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.045972, "relative_start": 1.702226161956787, "end": **********.045972, "relative_end": **********.045972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.047139, "relative_start": 1.7033929824829102, "end": **********.047139, "relative_end": **********.047139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.047541, "relative_start": 1.7037949562072754, "end": **********.047541, "relative_end": **********.047541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.05046, "relative_start": 1.706714153289795, "end": **********.05046, "relative_end": **********.05046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.051141, "relative_start": 1.707395076751709, "end": **********.051141, "relative_end": **********.051141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.051601, "relative_start": 1.707854986190796, "end": **********.051601, "relative_end": **********.051601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.051909, "relative_start": 1.7081630229949951, "end": **********.051909, "relative_end": **********.051909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.052355, "relative_start": 1.7086091041564941, "end": **********.052355, "relative_end": **********.052355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.052973, "relative_start": 1.7092270851135254, "end": **********.052973, "relative_end": **********.052973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.053305, "relative_start": 1.7095589637756348, "end": **********.053305, "relative_end": **********.053305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.055258, "relative_start": 1.7115120887756348, "end": **********.055258, "relative_end": **********.055258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.055902, "relative_start": 1.712156057357788, "end": **********.055902, "relative_end": **********.055902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.056341, "relative_start": 1.712594985961914, "end": **********.056341, "relative_end": **********.056341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.056642, "relative_start": 1.7128961086273193, "end": **********.056642, "relative_end": **********.056642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.05714, "relative_start": 1.7133941650390625, "end": **********.05714, "relative_end": **********.05714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.058196, "relative_start": 1.7144501209259033, "end": **********.058196, "relative_end": **********.058196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.058654, "relative_start": 1.7149081230163574, "end": **********.058654, "relative_end": **********.058654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.060591, "relative_start": 1.7168450355529785, "end": **********.060591, "relative_end": **********.060591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.06128, "relative_start": 1.717534065246582, "end": **********.06128, "relative_end": **********.06128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.06198, "relative_start": 1.7182340621948242, "end": **********.06198, "relative_end": **********.06198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.062349, "relative_start": 1.7186031341552734, "end": **********.062349, "relative_end": **********.062349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.062706, "relative_start": 1.7189600467681885, "end": **********.062706, "relative_end": **********.062706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.063158, "relative_start": 1.719412088394165, "end": **********.063158, "relative_end": **********.063158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.063441, "relative_start": 1.7196950912475586, "end": **********.063441, "relative_end": **********.063441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.063866, "relative_start": 1.7201199531555176, "end": **********.063866, "relative_end": **********.063866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.064501, "relative_start": 1.7207551002502441, "end": **********.064501, "relative_end": **********.064501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.064832, "relative_start": 1.721086025238037, "end": **********.064832, "relative_end": **********.064832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.066682, "relative_start": 1.7229361534118652, "end": **********.066682, "relative_end": **********.066682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.067328, "relative_start": 1.7235820293426514, "end": **********.067328, "relative_end": **********.067328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.067781, "relative_start": 1.7240350246429443, "end": **********.067781, "relative_end": **********.067781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.06808, "relative_start": 1.7243340015411377, "end": **********.06808, "relative_end": **********.06808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.068508, "relative_start": 1.724761962890625, "end": **********.068508, "relative_end": **********.068508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.069126, "relative_start": 1.7253799438476562, "end": **********.069126, "relative_end": **********.069126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.069469, "relative_start": 1.7257230281829834, "end": **********.069469, "relative_end": **********.069469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.072218, "relative_start": 1.7284719944000244, "end": **********.072218, "relative_end": **********.072218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.073284, "relative_start": 1.7295379638671875, "end": **********.073284, "relative_end": **********.073284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.075238, "relative_start": 1.731492042541504, "end": **********.075238, "relative_end": **********.075238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.076223, "relative_start": 1.7324769496917725, "end": **********.076223, "relative_end": **********.076223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.076904, "relative_start": 1.7331581115722656, "end": **********.076904, "relative_end": **********.076904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.077785, "relative_start": 1.734039068222046, "end": **********.077785, "relative_end": **********.077785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.078301, "relative_start": 1.7345550060272217, "end": **********.078301, "relative_end": **********.078301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.079212, "relative_start": 1.7354660034179688, "end": **********.079212, "relative_end": **********.079212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.080305, "relative_start": 1.7365591526031494, "end": **********.080305, "relative_end": **********.080305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.080842, "relative_start": 1.7370960712432861, "end": **********.080842, "relative_end": **********.080842, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.083786, "relative_start": 1.7400400638580322, "end": **********.083786, "relative_end": **********.083786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.084863, "relative_start": 1.741117000579834, "end": **********.084863, "relative_end": **********.084863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.085777, "relative_start": 1.7420310974121094, "end": **********.085777, "relative_end": **********.085777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.086395, "relative_start": 1.7426490783691406, "end": **********.086395, "relative_end": **********.086395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.086985, "relative_start": 1.743239164352417, "end": **********.086985, "relative_end": **********.086985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.087828, "relative_start": 1.744081974029541, "end": **********.087828, "relative_end": **********.087828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.088303, "relative_start": 1.7445571422576904, "end": **********.088303, "relative_end": **********.088303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.089004, "relative_start": 1.745258092880249, "end": **********.089004, "relative_end": **********.089004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.090225, "relative_start": 1.7464790344238281, "end": **********.090225, "relative_end": **********.090225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.091098, "relative_start": 1.747352123260498, "end": **********.091098, "relative_end": **********.091098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.09552, "relative_start": 1.7517740726470947, "end": **********.09552, "relative_end": **********.09552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.096877, "relative_start": 1.7531311511993408, "end": **********.096877, "relative_end": **********.096877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.098106, "relative_start": 1.7543599605560303, "end": **********.098106, "relative_end": **********.098106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.098727, "relative_start": 1.7549810409545898, "end": **********.098727, "relative_end": **********.098727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.09939, "relative_start": 1.7556440830230713, "end": **********.09939, "relative_end": **********.09939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.100329, "relative_start": 1.7565829753875732, "end": **********.100329, "relative_end": **********.100329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.100804, "relative_start": 1.7570581436157227, "end": **********.100804, "relative_end": **********.100804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.103201, "relative_start": 1.7594549655914307, "end": **********.103201, "relative_end": **********.103201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.104152, "relative_start": 1.7604060173034668, "end": **********.104152, "relative_end": **********.104152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.104678, "relative_start": 1.7609319686889648, "end": **********.104678, "relative_end": **********.104678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.105028, "relative_start": 1.761281967163086, "end": **********.105028, "relative_end": **********.105028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.105729, "relative_start": 1.7619831562042236, "end": **********.105729, "relative_end": **********.105729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.106681, "relative_start": 1.7629351615905762, "end": **********.106681, "relative_end": **********.106681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.107329, "relative_start": 1.7635829448699951, "end": **********.107329, "relative_end": **********.107329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.111681, "relative_start": 1.767935037612915, "end": **********.111681, "relative_end": **********.111681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.112988, "relative_start": 1.7692420482635498, "end": **********.112988, "relative_end": **********.112988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.114082, "relative_start": 1.7703361511230469, "end": **********.114082, "relative_end": **********.114082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.114479, "relative_start": 1.770733118057251, "end": **********.114479, "relative_end": **********.114479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.114861, "relative_start": 1.7711150646209717, "end": **********.114861, "relative_end": **********.114861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.11533, "relative_start": 1.7715840339660645, "end": **********.11533, "relative_end": **********.11533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.115619, "relative_start": 1.7718729972839355, "end": **********.115619, "relative_end": **********.115619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.116065, "relative_start": 1.7723190784454346, "end": **********.116065, "relative_end": **********.116065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.116733, "relative_start": 1.7729871273040771, "end": **********.116733, "relative_end": **********.116733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.117064, "relative_start": 1.7733180522918701, "end": **********.117064, "relative_end": **********.117064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.118917, "relative_start": 1.7751710414886475, "end": **********.118917, "relative_end": **********.118917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.119527, "relative_start": 1.7757811546325684, "end": **********.119527, "relative_end": **********.119527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.120007, "relative_start": 1.7762610912322998, "end": **********.120007, "relative_end": **********.120007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.120351, "relative_start": 1.7766051292419434, "end": **********.120351, "relative_end": **********.120351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.120771, "relative_start": 1.7770249843597412, "end": **********.120771, "relative_end": **********.120771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.121737, "relative_start": 1.7779910564422607, "end": **********.121737, "relative_end": **********.121737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.122093, "relative_start": 1.7783470153808594, "end": **********.122093, "relative_end": **********.122093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.12452, "relative_start": 1.7807741165161133, "end": **********.12452, "relative_end": **********.12452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.126017, "relative_start": 1.782271146774292, "end": **********.126017, "relative_end": **********.126017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.127805, "relative_start": 1.7840590476989746, "end": **********.127805, "relative_end": **********.127805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.128891, "relative_start": 1.7851450443267822, "end": **********.128891, "relative_end": **********.128891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.1294, "relative_start": 1.785654067993164, "end": **********.1294, "relative_end": **********.1294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.129976, "relative_start": 1.7862300872802734, "end": **********.129976, "relative_end": **********.129976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.130312, "relative_start": 1.7865660190582275, "end": **********.130312, "relative_end": **********.130312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.130845, "relative_start": 1.7870991230010986, "end": **********.130845, "relative_end": **********.130845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.131543, "relative_start": 1.787796974182129, "end": **********.131543, "relative_end": **********.131543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.13191, "relative_start": 1.7881641387939453, "end": **********.13191, "relative_end": **********.13191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.133932, "relative_start": 1.7901861667633057, "end": **********.133932, "relative_end": **********.133932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.134622, "relative_start": 1.7908761501312256, "end": **********.134622, "relative_end": **********.134622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.135074, "relative_start": 1.791327953338623, "end": **********.135074, "relative_end": **********.135074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.135389, "relative_start": 1.7916431427001953, "end": **********.135389, "relative_end": **********.135389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.135857, "relative_start": 1.7921111583709717, "end": **********.135857, "relative_end": **********.135857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.136502, "relative_start": 1.7927560806274414, "end": **********.136502, "relative_end": **********.136502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.136851, "relative_start": 1.793105125427246, "end": **********.136851, "relative_end": **********.136851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.139351, "relative_start": 1.795604944229126, "end": **********.139351, "relative_end": **********.139351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.140322, "relative_start": 1.7965760231018066, "end": **********.140322, "relative_end": **********.140322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.141776, "relative_start": 1.798030138015747, "end": **********.141776, "relative_end": **********.141776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.142473, "relative_start": 1.798727035522461, "end": **********.142473, "relative_end": **********.142473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.142965, "relative_start": 1.7992191314697266, "end": **********.142965, "relative_end": **********.142965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.143547, "relative_start": 1.7998011112213135, "end": **********.143547, "relative_end": **********.143547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.143925, "relative_start": 1.8001790046691895, "end": **********.143925, "relative_end": **********.143925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.144527, "relative_start": 1.800781011581421, "end": **********.144527, "relative_end": **********.144527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.145293, "relative_start": 1.8015470504760742, "end": **********.145293, "relative_end": **********.145293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.145704, "relative_start": 1.8019580841064453, "end": **********.145704, "relative_end": **********.145704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.147626, "relative_start": 1.803879976272583, "end": **********.147626, "relative_end": **********.147626, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.148282, "relative_start": 1.8045361042022705, "end": **********.148282, "relative_end": **********.148282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.148737, "relative_start": 1.8049910068511963, "end": **********.148737, "relative_end": **********.148737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.149081, "relative_start": 1.8053350448608398, "end": **********.149081, "relative_end": **********.149081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.149627, "relative_start": 1.8058810234069824, "end": **********.149627, "relative_end": **********.149627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.150263, "relative_start": 1.8065171241760254, "end": **********.150263, "relative_end": **********.150263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.150599, "relative_start": 1.8068530559539795, "end": **********.150599, "relative_end": **********.150599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.152563, "relative_start": 1.8088171482086182, "end": **********.152563, "relative_end": **********.152563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.153304, "relative_start": 1.8095581531524658, "end": **********.153304, "relative_end": **********.153304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.153881, "relative_start": 1.8101351261138916, "end": **********.153881, "relative_end": **********.153881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.154327, "relative_start": 1.8105809688568115, "end": **********.154327, "relative_end": **********.154327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.154782, "relative_start": 1.8110361099243164, "end": **********.154782, "relative_end": **********.154782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.155433, "relative_start": 1.8116869926452637, "end": **********.155433, "relative_end": **********.155433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.155794, "relative_start": 1.8120479583740234, "end": **********.155794, "relative_end": **********.155794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.158457, "relative_start": 1.814711093902588, "end": **********.158457, "relative_end": **********.158457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.159421, "relative_start": 1.8156750202178955, "end": **********.159421, "relative_end": **********.159421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.160048, "relative_start": 1.8163020610809326, "end": **********.160048, "relative_end": **********.160048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.160452, "relative_start": 1.8167059421539307, "end": **********.160452, "relative_end": **********.160452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.160918, "relative_start": 1.8171720504760742, "end": **********.160918, "relative_end": **********.160918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.161624, "relative_start": 1.817878007888794, "end": **********.161624, "relative_end": **********.161624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.161993, "relative_start": 1.8182470798492432, "end": **********.161993, "relative_end": **********.161993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.164978, "relative_start": 1.8212320804595947, "end": **********.164978, "relative_end": **********.164978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.166116, "relative_start": 1.8223700523376465, "end": **********.166116, "relative_end": **********.166116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.167132, "relative_start": 1.8233859539031982, "end": **********.167132, "relative_end": **********.167132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.167643, "relative_start": 1.823897123336792, "end": **********.167643, "relative_end": **********.167643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.168132, "relative_start": 1.8243861198425293, "end": **********.168132, "relative_end": **********.168132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.16879, "relative_start": 1.8250441551208496, "end": **********.16879, "relative_end": **********.16879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.16919, "relative_start": 1.825443983078003, "end": **********.16919, "relative_end": **********.16919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.169941, "relative_start": 1.8261950016021729, "end": **********.169941, "relative_end": **********.169941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.171059, "relative_start": 1.82731294631958, "end": **********.171059, "relative_end": **********.171059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.171596, "relative_start": 1.827850103378296, "end": **********.171596, "relative_end": **********.171596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.17469, "relative_start": 1.8309440612792969, "end": **********.17469, "relative_end": **********.17469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.175982, "relative_start": 1.8322360515594482, "end": **********.175982, "relative_end": **********.175982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.176916, "relative_start": 1.833169937133789, "end": **********.176916, "relative_end": **********.176916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.177739, "relative_start": 1.8339929580688477, "end": **********.177739, "relative_end": **********.177739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.1787, "relative_start": 1.834954023361206, "end": **********.1787, "relative_end": **********.1787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.179945, "relative_start": 1.8361990451812744, "end": **********.179945, "relative_end": **********.179945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.180532, "relative_start": 1.8367860317230225, "end": **********.180532, "relative_end": **********.180532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.183963, "relative_start": 1.840217113494873, "end": **********.183963, "relative_end": **********.183963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.186164, "relative_start": 1.8424179553985596, "end": **********.186164, "relative_end": **********.186164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.187477, "relative_start": 1.843731164932251, "end": **********.187477, "relative_end": **********.187477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.18799, "relative_start": 1.8442440032958984, "end": **********.18799, "relative_end": **********.18799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.188478, "relative_start": 1.8447320461273193, "end": **********.188478, "relative_end": **********.188478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.189163, "relative_start": 1.8454170227050781, "end": **********.189163, "relative_end": **********.189163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.189523, "relative_start": 1.8457770347595215, "end": **********.189523, "relative_end": **********.189523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.192714, "relative_start": 1.8489680290222168, "end": **********.192714, "relative_end": **********.192714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.194874, "relative_start": 1.851128101348877, "end": **********.194874, "relative_end": **********.194874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.196567, "relative_start": 1.8528211116790771, "end": **********.196567, "relative_end": **********.196567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.197334, "relative_start": 1.8535881042480469, "end": **********.197334, "relative_end": **********.197334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.197906, "relative_start": 1.8541600704193115, "end": **********.197906, "relative_end": **********.197906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.198771, "relative_start": 1.855025053024292, "end": **********.198771, "relative_end": **********.198771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.199284, "relative_start": 1.8555381298065186, "end": **********.199284, "relative_end": **********.199284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.200523, "relative_start": 1.8567769527435303, "end": **********.200523, "relative_end": **********.200523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.201839, "relative_start": 1.858093023300171, "end": **********.201839, "relative_end": **********.201839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.202544, "relative_start": 1.8587980270385742, "end": **********.202544, "relative_end": **********.202544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.207017, "relative_start": 1.8632709980010986, "end": **********.207017, "relative_end": **********.207017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.212314, "relative_start": 1.868567943572998, "end": **********.212314, "relative_end": **********.212314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.213086, "relative_start": 1.869339942932129, "end": **********.213086, "relative_end": **********.213086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.213813, "relative_start": 1.8700671195983887, "end": **********.213813, "relative_end": **********.213813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.214709, "relative_start": 1.8709630966186523, "end": **********.214709, "relative_end": **********.214709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.215579, "relative_start": 1.871833086013794, "end": **********.215579, "relative_end": **********.215579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.216071, "relative_start": 1.8723249435424805, "end": **********.216071, "relative_end": **********.216071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.218701, "relative_start": 1.8749549388885498, "end": **********.218701, "relative_end": **********.218701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.219552, "relative_start": 1.8758060932159424, "end": **********.219552, "relative_end": **********.219552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.22045, "relative_start": 1.8767039775848389, "end": **********.22045, "relative_end": **********.22045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.220941, "relative_start": 1.877195119857788, "end": **********.220941, "relative_end": **********.220941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.221353, "relative_start": 1.8776071071624756, "end": **********.221353, "relative_end": **********.221353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.221953, "relative_start": 1.8782069683074951, "end": **********.221953, "relative_end": **********.221953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.222374, "relative_start": 1.8786280155181885, "end": **********.222374, "relative_end": **********.222374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.223001, "relative_start": 1.8792550563812256, "end": **********.223001, "relative_end": **********.223001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.224117, "relative_start": 1.88037109375, "end": **********.224117, "relative_end": **********.224117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.225613, "relative_start": 1.8818671703338623, "end": **********.225613, "relative_end": **********.225613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.23053, "relative_start": 1.8867840766906738, "end": **********.23053, "relative_end": **********.23053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.232565, "relative_start": 1.8888189792633057, "end": **********.232565, "relative_end": **********.232565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.233629, "relative_start": 1.889883041381836, "end": **********.233629, "relative_end": **********.233629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.234309, "relative_start": 1.8905630111694336, "end": **********.234309, "relative_end": **********.234309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.235241, "relative_start": 1.8914949893951416, "end": **********.235241, "relative_end": **********.235241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.236941, "relative_start": 1.8931951522827148, "end": **********.236941, "relative_end": **********.236941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.237697, "relative_start": 1.8939509391784668, "end": **********.237697, "relative_end": **********.237697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.241425, "relative_start": 1.897679090499878, "end": **********.241425, "relative_end": **********.241425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.242973, "relative_start": 1.8992271423339844, "end": **********.242973, "relative_end": **********.242973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.244909, "relative_start": 1.901163101196289, "end": **********.244909, "relative_end": **********.244909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.245888, "relative_start": 1.90214204788208, "end": **********.245888, "relative_end": **********.245888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.247973, "relative_start": 1.9042270183563232, "end": **********.247973, "relative_end": **********.247973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.24964, "relative_start": 1.9058940410614014, "end": **********.24964, "relative_end": **********.24964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.250457, "relative_start": 1.9067111015319824, "end": **********.250457, "relative_end": **********.250457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.253538, "relative_start": 1.9097919464111328, "end": **********.253538, "relative_end": **********.253538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.255078, "relative_start": 1.911332130432129, "end": **********.255078, "relative_end": **********.255078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.256584, "relative_start": 1.9128379821777344, "end": **********.256584, "relative_end": **********.256584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.257165, "relative_start": 1.9134190082550049, "end": **********.257165, "relative_end": **********.257165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.258576, "relative_start": 1.914829969406128, "end": **********.258576, "relative_end": **********.258576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.260249, "relative_start": 1.9165029525756836, "end": **********.260249, "relative_end": **********.260249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.260839, "relative_start": 1.91709303855896, "end": **********.260839, "relative_end": **********.260839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.261726, "relative_start": 1.9179799556732178, "end": **********.261726, "relative_end": **********.261726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.262471, "relative_start": 1.9187250137329102, "end": **********.262471, "relative_end": **********.262471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.262832, "relative_start": 1.91908597946167, "end": **********.262832, "relative_end": **********.262832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.264813, "relative_start": 1.9210669994354248, "end": **********.264813, "relative_end": **********.264813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.265625, "relative_start": 1.9218790531158447, "end": **********.265625, "relative_end": **********.265625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.266208, "relative_start": 1.922461986541748, "end": **********.266208, "relative_end": **********.266208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.266585, "relative_start": 1.9228391647338867, "end": **********.266585, "relative_end": **********.266585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.267018, "relative_start": 1.9232721328735352, "end": **********.267018, "relative_end": **********.267018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.26794, "relative_start": 1.924194097518921, "end": **********.26794, "relative_end": **********.26794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.268458, "relative_start": 1.9247119426727295, "end": **********.268458, "relative_end": **********.268458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.272478, "relative_start": 1.92873215675354, "end": **********.272478, "relative_end": **********.272478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.274102, "relative_start": 1.9303560256958008, "end": **********.274102, "relative_end": **********.274102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.275427, "relative_start": 1.9316811561584473, "end": **********.275427, "relative_end": **********.275427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.276159, "relative_start": 1.932413101196289, "end": **********.276159, "relative_end": **********.276159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.277128, "relative_start": 1.9333820343017578, "end": **********.277128, "relative_end": **********.277128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.278614, "relative_start": 1.9348680973052979, "end": **********.278614, "relative_end": **********.278614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.279491, "relative_start": 1.9357450008392334, "end": **********.279491, "relative_end": **********.279491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.282427, "relative_start": 1.9386811256408691, "end": **********.282427, "relative_end": **********.282427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.283767, "relative_start": 1.94002103805542, "end": **********.283767, "relative_end": **********.283767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.284514, "relative_start": 1.9407680034637451, "end": **********.284514, "relative_end": **********.284514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.284905, "relative_start": 1.9411590099334717, "end": **********.284905, "relative_end": **********.284905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.285265, "relative_start": 1.941519021987915, "end": **********.285265, "relative_end": **********.285265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.285723, "relative_start": 1.9419770240783691, "end": **********.285723, "relative_end": **********.285723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.286006, "relative_start": 1.9422600269317627, "end": **********.286006, "relative_end": **********.286006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.286446, "relative_start": 1.9427001476287842, "end": **********.286446, "relative_end": **********.286446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.287107, "relative_start": 1.9433610439300537, "end": **********.287107, "relative_end": **********.287107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.287508, "relative_start": 1.9437620639801025, "end": **********.287508, "relative_end": **********.287508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.290911, "relative_start": 1.9471650123596191, "end": **********.290911, "relative_end": **********.290911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.29228, "relative_start": 1.9485340118408203, "end": **********.29228, "relative_end": **********.29228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.293307, "relative_start": 1.9495611190795898, "end": **********.293307, "relative_end": **********.293307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.295004, "relative_start": 1.9512579441070557, "end": **********.295004, "relative_end": **********.295004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.296589, "relative_start": 1.9528429508209229, "end": **********.296589, "relative_end": **********.296589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.299052, "relative_start": 1.955306053161621, "end": **********.299052, "relative_end": **********.299052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.300079, "relative_start": 1.9563331604003906, "end": **********.300079, "relative_end": **********.300079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.304747, "relative_start": 1.9610011577606201, "end": **********.304747, "relative_end": **********.304747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.30645, "relative_start": 1.9627039432525635, "end": **********.30645, "relative_end": **********.30645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.307324, "relative_start": 1.9635779857635498, "end": **********.307324, "relative_end": **********.307324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.308211, "relative_start": 1.9644651412963867, "end": **********.308211, "relative_end": **********.308211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.308971, "relative_start": 1.9652249813079834, "end": **********.308971, "relative_end": **********.308971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.310642, "relative_start": 1.9668960571289062, "end": **********.310642, "relative_end": **********.310642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.311356, "relative_start": 1.9676101207733154, "end": **********.311356, "relative_end": **********.311356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.313755, "relative_start": 1.9700090885162354, "end": **********.313755, "relative_end": **********.313755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.31462, "relative_start": 1.9708740711212158, "end": **********.31462, "relative_end": **********.31462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.315154, "relative_start": 1.9714081287384033, "end": **********.315154, "relative_end": **********.315154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.315606, "relative_start": 1.9718601703643799, "end": **********.315606, "relative_end": **********.315606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.31642, "relative_start": 1.9726741313934326, "end": **********.31642, "relative_end": **********.31642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.317515, "relative_start": 1.973768949508667, "end": **********.317515, "relative_end": **********.317515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.318053, "relative_start": 1.9743070602416992, "end": **********.318053, "relative_end": **********.318053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.321849, "relative_start": 1.9781031608581543, "end": **********.321849, "relative_end": **********.321849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.322975, "relative_start": 1.9792289733886719, "end": **********.322975, "relative_end": **********.322975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.324183, "relative_start": 1.9804370403289795, "end": **********.324183, "relative_end": **********.324183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.324857, "relative_start": 1.9811110496520996, "end": **********.324857, "relative_end": **********.324857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.325504, "relative_start": 1.9817581176757812, "end": **********.325504, "relative_end": **********.325504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.326375, "relative_start": 1.9826290607452393, "end": **********.326375, "relative_end": **********.326375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.32688, "relative_start": 1.9831340312957764, "end": **********.32688, "relative_end": **********.32688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.32768, "relative_start": 1.9839341640472412, "end": **********.32768, "relative_end": **********.32768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.328889, "relative_start": 1.9851429462432861, "end": **********.328889, "relative_end": **********.328889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.329488, "relative_start": 1.9857420921325684, "end": **********.329488, "relative_end": **********.329488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.331512, "relative_start": 1.9877660274505615, "end": **********.331512, "relative_end": **********.331512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.332215, "relative_start": 1.988469123840332, "end": **********.332215, "relative_end": **********.332215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.332928, "relative_start": 1.9891819953918457, "end": **********.332928, "relative_end": **********.332928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.333318, "relative_start": 1.9895720481872559, "end": **********.333318, "relative_end": **********.333318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.333683, "relative_start": 1.9899370670318604, "end": **********.333683, "relative_end": **********.333683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.334348, "relative_start": 1.9906020164489746, "end": **********.334348, "relative_end": **********.334348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.334695, "relative_start": 1.9909491539001465, "end": **********.334695, "relative_end": **********.334695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.335051, "relative_start": 1.9913051128387451, "end": **********.335051, "relative_end": **********.335051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.335511, "relative_start": 1.991765022277832, "end": **********.335511, "relative_end": **********.335511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.335821, "relative_start": 1.992074966430664, "end": **********.335821, "relative_end": **********.335821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.336288, "relative_start": 1.992542028427124, "end": **********.336288, "relative_end": **********.336288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.336941, "relative_start": 1.9931950569152832, "end": **********.336941, "relative_end": **********.336941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.337284, "relative_start": 1.9935381412506104, "end": **********.337284, "relative_end": **********.337284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.339169, "relative_start": 1.9954230785369873, "end": **********.339169, "relative_end": **********.339169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.339812, "relative_start": 1.9960660934448242, "end": **********.339812, "relative_end": **********.339812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.340263, "relative_start": 1.9965169429779053, "end": **********.340263, "relative_end": **********.340263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.340575, "relative_start": 1.9968290328979492, "end": **********.340575, "relative_end": **********.340575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.341977, "relative_start": 1.9982309341430664, "end": **********.341977, "relative_end": **********.341977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.342998, "relative_start": 1.9992520809173584, "end": **********.342998, "relative_end": **********.342998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.343614, "relative_start": 1.9998681545257568, "end": **********.343614, "relative_end": **********.343614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.346278, "relative_start": 2.0025320053100586, "end": **********.346278, "relative_end": **********.346278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.347956, "relative_start": 2.0042099952697754, "end": **********.347956, "relative_end": **********.347956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.349813, "relative_start": 2.0060670375823975, "end": **********.349813, "relative_end": **********.349813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.35072, "relative_start": 2.0069739818573, "end": **********.35072, "relative_end": **********.35072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.351606, "relative_start": 2.007859945297241, "end": **********.351606, "relative_end": **********.351606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.352505, "relative_start": 2.008759021759033, "end": **********.352505, "relative_end": **********.352505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.352877, "relative_start": 2.0091309547424316, "end": **********.352877, "relative_end": **********.352877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.354758, "relative_start": 2.011012077331543, "end": **********.354758, "relative_end": **********.354758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.355383, "relative_start": 2.011636972427368, "end": **********.355383, "relative_end": **********.355383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.355828, "relative_start": 2.012082099914551, "end": **********.355828, "relative_end": **********.355828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.356135, "relative_start": 2.0123889446258545, "end": **********.356135, "relative_end": **********.356135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.356561, "relative_start": 2.012814998626709, "end": **********.356561, "relative_end": **********.356561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.357243, "relative_start": 2.0134971141815186, "end": **********.357243, "relative_end": **********.357243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.357909, "relative_start": 2.014163017272949, "end": **********.357909, "relative_end": **********.357909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.360528, "relative_start": 2.01678204536438, "end": **********.360528, "relative_end": **********.360528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.36161, "relative_start": 2.0178639888763428, "end": **********.36161, "relative_end": **********.36161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.362372, "relative_start": 2.0186259746551514, "end": **********.362372, "relative_end": **********.362372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.362738, "relative_start": 2.0189919471740723, "end": **********.362738, "relative_end": **********.362738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.363249, "relative_start": 2.019503116607666, "end": **********.363249, "relative_end": **********.363249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.363929, "relative_start": 2.0201830863952637, "end": **********.363929, "relative_end": **********.363929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.364295, "relative_start": 2.0205490589141846, "end": **********.364295, "relative_end": **********.364295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.366372, "relative_start": 2.0226261615753174, "end": **********.366372, "relative_end": **********.366372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.367025, "relative_start": 2.0232789516448975, "end": **********.367025, "relative_end": **********.367025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.367475, "relative_start": 2.023729085922241, "end": **********.367475, "relative_end": **********.367475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.36778, "relative_start": 2.024034023284912, "end": **********.36778, "relative_end": **********.36778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.368219, "relative_start": 2.024472951889038, "end": **********.368219, "relative_end": **********.368219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.368848, "relative_start": 2.025102138519287, "end": **********.368848, "relative_end": **********.368848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.369183, "relative_start": 2.025437116622925, "end": **********.369183, "relative_end": **********.369183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.371041, "relative_start": 2.0272951126098633, "end": **********.371041, "relative_end": **********.371041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.37167, "relative_start": 2.027924060821533, "end": **********.37167, "relative_end": **********.37167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.37209, "relative_start": 2.02834415435791, "end": **********.37209, "relative_end": **********.37209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.372387, "relative_start": 2.0286409854888916, "end": **********.372387, "relative_end": **********.372387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.372813, "relative_start": 2.029067039489746, "end": **********.372813, "relative_end": **********.372813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.373723, "relative_start": 2.0299770832061768, "end": **********.373723, "relative_end": **********.373723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.374243, "relative_start": 2.0304970741271973, "end": **********.374243, "relative_end": **********.374243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.378244, "relative_start": 2.0344979763031006, "end": **********.378244, "relative_end": **********.378244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.379443, "relative_start": 2.0356969833374023, "end": **********.379443, "relative_end": **********.379443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.380137, "relative_start": 2.036391019821167, "end": **********.380137, "relative_end": **********.380137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.380586, "relative_start": 2.0368399620056152, "end": **********.380586, "relative_end": **********.380586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.381262, "relative_start": 2.0375161170959473, "end": **********.381262, "relative_end": **********.381262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.382245, "relative_start": 2.038499116897583, "end": **********.382245, "relative_end": **********.382245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.382733, "relative_start": 2.038987159729004, "end": **********.382733, "relative_end": **********.382733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.386453, "relative_start": 2.0427069664001465, "end": **********.386453, "relative_end": **********.386453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.387446, "relative_start": 2.0436999797821045, "end": **********.387446, "relative_end": **********.387446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.388053, "relative_start": 2.044306993484497, "end": **********.388053, "relative_end": **********.388053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.388479, "relative_start": 2.0447330474853516, "end": **********.388479, "relative_end": **********.388479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.389144, "relative_start": 2.045397996902466, "end": **********.389144, "relative_end": **********.389144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.390135, "relative_start": 2.046389102935791, "end": **********.390135, "relative_end": **********.390135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.390628, "relative_start": 2.046882152557373, "end": **********.390628, "relative_end": **********.390628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.39393, "relative_start": 2.0501840114593506, "end": **********.39393, "relative_end": **********.39393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.394878, "relative_start": 2.0511319637298584, "end": **********.394878, "relative_end": **********.394878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.39542, "relative_start": 2.0516741275787354, "end": **********.39542, "relative_end": **********.39542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.395771, "relative_start": 2.052025079727173, "end": **********.395771, "relative_end": **********.395771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.396316, "relative_start": 2.052570104598999, "end": **********.396316, "relative_end": **********.396316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.39704, "relative_start": 2.0532939434051514, "end": **********.39704, "relative_end": **********.39704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.397434, "relative_start": 2.0536880493164062, "end": **********.397434, "relative_end": **********.397434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.399546, "relative_start": 2.055799961090088, "end": **********.399546, "relative_end": **********.399546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.400462, "relative_start": 2.056715965270996, "end": **********.400462, "relative_end": **********.400462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.400958, "relative_start": 2.0572121143341064, "end": **********.400958, "relative_end": **********.400958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.401296, "relative_start": 2.0575499534606934, "end": **********.401296, "relative_end": **********.401296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.401777, "relative_start": 2.0580310821533203, "end": **********.401777, "relative_end": **********.401777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.402448, "relative_start": 2.058701992034912, "end": **********.402448, "relative_end": **********.402448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.402805, "relative_start": 2.0590591430664062, "end": **********.402805, "relative_end": **********.402805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.404919, "relative_start": 2.0611729621887207, "end": **********.404919, "relative_end": **********.404919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.406025, "relative_start": 2.062278985977173, "end": **********.406025, "relative_end": **********.406025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.406683, "relative_start": 2.062937021255493, "end": **********.406683, "relative_end": **********.406683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.407123, "relative_start": 2.0633771419525146, "end": **********.407123, "relative_end": **********.407123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.408101, "relative_start": 2.0643551349639893, "end": **********.408101, "relative_end": **********.408101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.409407, "relative_start": 2.0656609535217285, "end": **********.409407, "relative_end": **********.409407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.410048, "relative_start": 2.0663020610809326, "end": **********.410048, "relative_end": **********.410048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.412459, "relative_start": 2.0687129497528076, "end": **********.412459, "relative_end": **********.412459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.413501, "relative_start": 2.0697550773620605, "end": **********.413501, "relative_end": **********.413501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.414462, "relative_start": 2.070716142654419, "end": **********.414462, "relative_end": **********.414462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.415026, "relative_start": 2.071280002593994, "end": **********.415026, "relative_end": **********.415026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.415448, "relative_start": 2.071702003479004, "end": **********.415448, "relative_end": **********.415448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.416276, "relative_start": 2.0725300312042236, "end": **********.416276, "relative_end": **********.416276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.41686, "relative_start": 2.0731141567230225, "end": **********.41686, "relative_end": **********.41686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.417329, "relative_start": 2.0735831260681152, "end": **********.417329, "relative_end": **********.417329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.417882, "relative_start": 2.0741360187530518, "end": **********.417882, "relative_end": **********.417882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.418226, "relative_start": 2.0744800567626953, "end": **********.418226, "relative_end": **********.418226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.418789, "relative_start": 2.075042963027954, "end": **********.418789, "relative_end": **********.418789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.419664, "relative_start": 2.075917959213257, "end": **********.419664, "relative_end": **********.419664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.420141, "relative_start": 2.076395034790039, "end": **********.420141, "relative_end": **********.420141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.423267, "relative_start": 2.0795209407806396, "end": **********.423267, "relative_end": **********.423267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.424796, "relative_start": 2.081050157546997, "end": **********.424796, "relative_end": **********.424796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.425745, "relative_start": 2.0819990634918213, "end": **********.425745, "relative_end": **********.425745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.426284, "relative_start": 2.08253812789917, "end": **********.426284, "relative_end": **********.426284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.427089, "relative_start": 2.083343029022217, "end": **********.427089, "relative_end": **********.427089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.428184, "relative_start": 2.0844380855560303, "end": **********.428184, "relative_end": **********.428184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.428748, "relative_start": 2.0850019454956055, "end": **********.428748, "relative_end": **********.428748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.432665, "relative_start": 2.088919162750244, "end": **********.432665, "relative_end": **********.432665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.434856, "relative_start": 2.0911099910736084, "end": **********.434856, "relative_end": **********.434856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.436573, "relative_start": 2.092827081680298, "end": **********.436573, "relative_end": **********.436573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.437274, "relative_start": 2.0935280323028564, "end": **********.437274, "relative_end": **********.437274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.437851, "relative_start": 2.0941050052642822, "end": **********.437851, "relative_end": **********.437851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.438643, "relative_start": 2.0948970317840576, "end": **********.438643, "relative_end": **********.438643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.439113, "relative_start": 2.095366954803467, "end": **********.439113, "relative_end": **********.439113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.439618, "relative_start": 2.095872163772583, "end": **********.439618, "relative_end": **********.439618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.440565, "relative_start": 2.0968191623687744, "end": **********.440565, "relative_end": **********.440565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.441341, "relative_start": 2.097594976425171, "end": **********.441341, "relative_end": **********.441341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.446012, "relative_start": 2.1022660732269287, "end": **********.446012, "relative_end": **********.446012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.447331, "relative_start": 2.1035850048065186, "end": **********.447331, "relative_end": **********.447331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.448686, "relative_start": 2.1049399375915527, "end": **********.448686, "relative_end": **********.448686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.449582, "relative_start": 2.1058361530303955, "end": **********.449582, "relative_end": **********.449582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.450277, "relative_start": 2.1065311431884766, "end": **********.450277, "relative_end": **********.450277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.450986, "relative_start": 2.1072399616241455, "end": **********.450986, "relative_end": **********.450986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.451305, "relative_start": 2.1075589656829834, "end": **********.451305, "relative_end": **********.451305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.451994, "relative_start": 2.108247995376587, "end": **********.451994, "relative_end": **********.451994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.452982, "relative_start": 2.109236001968384, "end": **********.452982, "relative_end": **********.452982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.453437, "relative_start": 2.1096911430358887, "end": **********.453437, "relative_end": **********.453437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.455459, "relative_start": 2.111713171005249, "end": **********.455459, "relative_end": **********.455459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.456282, "relative_start": 2.1125359535217285, "end": **********.456282, "relative_end": **********.456282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.457019, "relative_start": 2.1132731437683105, "end": **********.457019, "relative_end": **********.457019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.45737, "relative_start": 2.113624095916748, "end": **********.45737, "relative_end": **********.45737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.458343, "relative_start": 2.1145970821380615, "end": **********.458343, "relative_end": **********.458343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.459601, "relative_start": 2.1158549785614014, "end": **********.459601, "relative_end": **********.459601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.460147, "relative_start": 2.116400957107544, "end": **********.460147, "relative_end": **********.460147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.463701, "relative_start": 2.119955062866211, "end": **********.463701, "relative_end": **********.463701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.464832, "relative_start": 2.1210861206054688, "end": **********.464832, "relative_end": **********.464832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.465953, "relative_start": 2.1222071647644043, "end": **********.465953, "relative_end": **********.465953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.466487, "relative_start": 2.1227409839630127, "end": **********.466487, "relative_end": **********.466487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.467064, "relative_start": 2.1233179569244385, "end": **********.467064, "relative_end": **********.467064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.467581, "relative_start": 2.1238350868225098, "end": **********.467581, "relative_end": **********.467581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.467973, "relative_start": 2.1242270469665527, "end": **********.467973, "relative_end": **********.467973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.468632, "relative_start": 2.1248860359191895, "end": **********.468632, "relative_end": **********.468632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.469623, "relative_start": 2.1258771419525146, "end": **********.469623, "relative_end": **********.469623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.470098, "relative_start": 2.126352071762085, "end": **********.470098, "relative_end": **********.470098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.473518, "relative_start": 2.1297719478607178, "end": **********.473518, "relative_end": **********.473518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.474663, "relative_start": 2.1309170722961426, "end": **********.474663, "relative_end": **********.474663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.475742, "relative_start": 2.1319961547851562, "end": **********.475742, "relative_end": **********.475742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.476291, "relative_start": 2.132544994354248, "end": **********.476291, "relative_end": **********.476291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.477018, "relative_start": 2.133272171020508, "end": **********.477018, "relative_end": **********.477018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.478069, "relative_start": 2.1343231201171875, "end": **********.478069, "relative_end": **********.478069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.478572, "relative_start": 2.1348259449005127, "end": **********.478572, "relative_end": **********.478572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.482434, "relative_start": 2.138688087463379, "end": **********.482434, "relative_end": **********.482434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.483738, "relative_start": 2.1399919986724854, "end": **********.483738, "relative_end": **********.483738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.484506, "relative_start": 2.1407599449157715, "end": **********.484506, "relative_end": **********.484506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.485001, "relative_start": 2.1412551403045654, "end": **********.485001, "relative_end": **********.485001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.485791, "relative_start": 2.142045021057129, "end": **********.485791, "relative_end": **********.485791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.486923, "relative_start": 2.143177032470703, "end": **********.486923, "relative_end": **********.486923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.487509, "relative_start": 2.1437630653381348, "end": **********.487509, "relative_end": **********.487509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.491914, "relative_start": 2.1481680870056152, "end": **********.491914, "relative_end": **********.491914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.493636, "relative_start": 2.1498899459838867, "end": **********.493636, "relative_end": **********.493636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.495032, "relative_start": 2.1512861251831055, "end": **********.495032, "relative_end": **********.495032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.495655, "relative_start": 2.151909112930298, "end": **********.495655, "relative_end": **********.495655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.496377, "relative_start": 2.1526310443878174, "end": **********.496377, "relative_end": **********.496377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.497097, "relative_start": 2.153351068496704, "end": **********.497097, "relative_end": **********.497097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.497519, "relative_start": 2.153773069381714, "end": **********.497519, "relative_end": **********.497519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.500188, "relative_start": 2.156442165374756, "end": **********.500188, "relative_end": **********.500188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.501409, "relative_start": 2.157663106918335, "end": **********.501409, "relative_end": **********.501409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.502542, "relative_start": 2.1587960720062256, "end": **********.502542, "relative_end": **********.502542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.503086, "relative_start": 2.1593401432037354, "end": **********.503086, "relative_end": **********.503086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.503847, "relative_start": 2.1601009368896484, "end": **********.503847, "relative_end": **********.503847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.505032, "relative_start": 2.1612861156463623, "end": **********.505032, "relative_end": **********.505032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.505658, "relative_start": 2.161911964416504, "end": **********.505658, "relative_end": **********.505658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.511343, "relative_start": 2.1675970554351807, "end": **********.511343, "relative_end": **********.511343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.512638, "relative_start": 2.1688921451568604, "end": **********.512638, "relative_end": **********.512638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.513939, "relative_start": 2.1701929569244385, "end": **********.513939, "relative_end": **********.513939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.514575, "relative_start": 2.1708290576934814, "end": **********.514575, "relative_end": **********.514575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.51528, "relative_start": 2.1715340614318848, "end": **********.51528, "relative_end": **********.51528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.516936, "relative_start": 2.173190116882324, "end": **********.516936, "relative_end": **********.516936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.517718, "relative_start": 2.1739721298217773, "end": **********.517718, "relative_end": **********.517718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.519912, "relative_start": 2.17616605758667, "end": **********.519912, "relative_end": **********.519912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.520702, "relative_start": 2.1769559383392334, "end": **********.520702, "relative_end": **********.520702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.521525, "relative_start": 2.177778959274292, "end": **********.521525, "relative_end": **********.521525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.521994, "relative_start": 2.178248167037964, "end": **********.521994, "relative_end": **********.521994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.522476, "relative_start": 2.178730010986328, "end": **********.522476, "relative_end": **********.522476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.523088, "relative_start": 2.179342031478882, "end": **********.523088, "relative_end": **********.523088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.523483, "relative_start": 2.179737091064453, "end": **********.523483, "relative_end": **********.523483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.524128, "relative_start": 2.180382013320923, "end": **********.524128, "relative_end": **********.524128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.525601, "relative_start": 2.1818549633026123, "end": **********.525601, "relative_end": **********.525601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.526272, "relative_start": 2.182526111602783, "end": **********.526272, "relative_end": **********.526272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.531203, "relative_start": 2.1874570846557617, "end": **********.531203, "relative_end": **********.531203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.532769, "relative_start": 2.189023017883301, "end": **********.532769, "relative_end": **********.532769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.533526, "relative_start": 2.1897799968719482, "end": **********.533526, "relative_end": **********.533526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.534091, "relative_start": 2.190345048904419, "end": **********.534091, "relative_end": **********.534091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.534783, "relative_start": 2.1910369396209717, "end": **********.534783, "relative_end": **********.534783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.5358, "relative_start": 2.192054033279419, "end": **********.5358, "relative_end": **********.5358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.536279, "relative_start": 2.192533016204834, "end": **********.536279, "relative_end": **********.536279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.540019, "relative_start": 2.1962730884552, "end": **********.540019, "relative_end": **********.540019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.541207, "relative_start": 2.1974611282348633, "end": **********.541207, "relative_end": **********.541207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.542454, "relative_start": 2.1987080574035645, "end": **********.542454, "relative_end": **********.542454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.543743, "relative_start": 2.1999969482421875, "end": **********.543743, "relative_end": **********.543743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.544887, "relative_start": 2.201141119003296, "end": **********.544887, "relative_end": **********.544887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.545826, "relative_start": 2.202080011367798, "end": **********.545826, "relative_end": **********.545826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.546252, "relative_start": 2.2025060653686523, "end": **********.546252, "relative_end": **********.546252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.548318, "relative_start": 2.2045719623565674, "end": **********.548318, "relative_end": **********.548318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.54903, "relative_start": 2.2052841186523438, "end": **********.54903, "relative_end": **********.54903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.549632, "relative_start": 2.205886125564575, "end": **********.549632, "relative_end": **********.549632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.55004, "relative_start": 2.206294059753418, "end": **********.55004, "relative_end": **********.55004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.550511, "relative_start": 2.2067649364471436, "end": **********.550511, "relative_end": **********.550511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.551168, "relative_start": 2.2074220180511475, "end": **********.551168, "relative_end": **********.551168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.551539, "relative_start": 2.2077929973602295, "end": **********.551539, "relative_end": **********.551539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.553659, "relative_start": 2.2099130153656006, "end": **********.553659, "relative_end": **********.553659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.554602, "relative_start": 2.2108559608459473, "end": **********.554602, "relative_end": **********.554602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.555623, "relative_start": 2.2118771076202393, "end": **********.555623, "relative_end": **********.555623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.556194, "relative_start": 2.2124481201171875, "end": **********.556194, "relative_end": **********.556194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.556774, "relative_start": 2.2130279541015625, "end": **********.556774, "relative_end": **********.556774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.557473, "relative_start": 2.2137269973754883, "end": **********.557473, "relative_end": **********.557473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.558187, "relative_start": 2.2144410610198975, "end": **********.558187, "relative_end": **********.558187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.559332, "relative_start": 2.215585947036743, "end": **********.559332, "relative_end": **********.559332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.561303, "relative_start": 2.217556953430176, "end": **********.561303, "relative_end": **********.561303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.562273, "relative_start": 2.21852707862854, "end": **********.562273, "relative_end": **********.562273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.565339, "relative_start": 2.221593141555786, "end": **********.565339, "relative_end": **********.565339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.566228, "relative_start": 2.2224819660186768, "end": **********.566228, "relative_end": **********.566228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.566765, "relative_start": 2.2230191230773926, "end": **********.566765, "relative_end": **********.566765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.56711, "relative_start": 2.2233641147613525, "end": **********.56711, "relative_end": **********.56711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.567597, "relative_start": 2.223850965499878, "end": **********.567597, "relative_end": **********.567597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.568281, "relative_start": 2.2245349884033203, "end": **********.568281, "relative_end": **********.568281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.568631, "relative_start": 2.2248849868774414, "end": **********.568631, "relative_end": **********.568631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.570494, "relative_start": 2.226747989654541, "end": **********.570494, "relative_end": **********.570494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.571136, "relative_start": 2.2273900508880615, "end": **********.571136, "relative_end": **********.571136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-prices.columns.name", "start": **********.571571, "relative_start": 2.227825164794922, "end": **********.571571, "relative_end": **********.571571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.storehouse_management", "start": **********.571874, "relative_start": 2.228127956390381, "end": **********.571874, "relative_end": **********.571874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.572303, "relative_start": 2.2285571098327637, "end": **********.572303, "relative_end": **********.572303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.572929, "relative_start": 2.2291829586029053, "end": **********.572929, "relative_end": **********.572929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::product-inventory.columns.quantity", "start": **********.573263, "relative_start": 2.2295169830322266, "end": **********.573263, "relative_end": **********.573263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.578796, "relative_start": 2.2350499629974365, "end": **********.578796, "relative_end": **********.578796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.580089, "relative_start": 2.2363431453704834, "end": **********.580089, "relative_end": **********.580089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.583606, "relative_start": 2.2398600578308105, "end": **********.584128, "relative_end": **********.584128, "duration": 0.0005218982696533203, "duration_str": "522μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 58017816, "peak_usage_str": "55MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "adawliahshop.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 510, "nb_templates": 510, "templates": [{"name": "1x core/table::table-info", "param_count": null, "params": [], "start": **********.361959, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table-info"}, {"name": "1x __components::c33cfbd01dd1d76718fcd68287a40728", "param_count": null, "params": [], "start": **********.367499, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/c33cfbd01dd1d76718fcd68287a40728.blade.php__components::c33cfbd01dd1d76718fcd68287a40728", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fc33cfbd01dd1d76718fcd68287a40728.blade.php&line=1", "ajax": false, "filename": "c33cfbd01dd1d76718fcd68287a40728.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c33cfbd01dd1d76718fcd68287a40728"}, {"name": "1x 8def1252668913628243c4d363bee1ef::badge", "param_count": null, "params": [], "start": **********.369772, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/badge.blade.php8def1252668913628243c4d363bee1ef::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::badge"}, {"name": "85x plugins/ecommerce::product-prices.columns.name", "param_count": null, "params": [], "start": **********.486387, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/plugins/ecommerce/resources/views/product-prices/columns/name.blade.phpplugins/ecommerce::product-prices.columns.name", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fproduct-prices%2Fcolumns%2Fname.blade.php&line=1", "ajax": false, "filename": "name.blade.php", "line": "?"}, "render_count": 85, "name_original": "plugins/ecommerce::product-prices.columns.name"}, {"name": "85x plugins/ecommerce::product-inventory.columns.storehouse_management", "param_count": null, "params": [], "start": **********.557409, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/plugins/ecommerce/resources/views/product-inventory/columns/storehouse_management.blade.phpplugins/ecommerce::product-inventory.columns.storehouse_management", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fproduct-inventory%2Fcolumns%2Fstorehouse_management.blade.php&line=1", "ajax": false, "filename": "storehouse_management.blade.php", "line": "?"}, "render_count": 85, "name_original": "plugins/ecommerce::product-inventory.columns.storehouse_management"}, {"name": "85x plugins/ecommerce::product-inventory.columns.quantity", "param_count": null, "params": [], "start": **********.783376, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/plugins/ecommerce/resources/views/product-inventory/columns/quantity.blade.phpplugins/ecommerce::product-inventory.columns.quantity", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fproduct-inventory%2Fcolumns%2Fquantity.blade.php&line=1", "ajax": false, "filename": "quantity.blade.php", "line": "?"}, "render_count": 85, "name_original": "plugins/ecommerce::product-inventory.columns.quantity"}, {"name": "126x 8def1252668913628243c4d363bee1ef::form.select", "param_count": null, "params": [], "start": **********.045952, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/select.blade.php8def1252668913628243c4d363bee1ef::form.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 126, "name_original": "8def1252668913628243c4d363bee1ef::form.select"}, {"name": "126x 8def1252668913628243c4d363bee1ef::form-group", "param_count": null, "params": [], "start": **********.047119, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form-group.blade.php8def1252668913628243c4d363bee1ef::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 126, "name_original": "8def1252668913628243c4d363bee1ef::form-group"}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08356, "accumulated_duration_str": "83.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.309119, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 0, "width_percent": 0.562}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.313525, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "adawliahshop", "explain": null, "start_percent": 0.562, "width_percent": 0.455}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.318918, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 1.017, "width_percent": 0.443}, {"sql": "select count(*) as aggregate from (select `ec_products`.`id`, `ec_products`.`name`, `ec_products`.`image`, `ec_products`.`images`, `ec_products`.`sku`, `ec_products`.`is_variation`, `pv`.`configurable_product_id` as `parent_product_id`, `va`.`variation_attributes`, `vc`.`variations_count`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`with_storehouse_management` from `ec_products` left join `ec_product_variations` as `pv` on `ec_products`.`id` = `pv`.`product_id` and `ec_products`.`is_variation` = 1 left join (select `pv`.`product_id`, GROUP_CONCAT(CONCAT(pas.title, ': ', pa.title) ORDER BY pas.order, pa.order SEPARATOR ', ') as variation_attributes from `ec_product_variations` as `pv` left join `ec_product_variation_items` as `pvi` on `pvi`.`variation_id` = `pv`.`id` left join `ec_product_attributes` as `pa` on `pa`.`id` = `pvi`.`attribute_id` left join `ec_product_attribute_sets` as `pas` on `pas`.`id` = `pa`.`attribute_set_id` group by `pv`.`product_id`) as `va` on `ec_products`.`id` = `va`.`product_id` left join (select `configurable_product_id`, COUNT(*) as variations_count from `ec_product_variations` group by `configurable_product_id`) as `vc` on `ec_products`.`id` = `vc`.`configurable_product_id` order by `ec_products`.`name` asc, `parent_product_id` asc) count_row_table", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/DataTableAbstract.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 709}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 157}, {"index": 18, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}], "start": **********.3752499, "duration": 0.02182, "duration_str": "21.82ms", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:174", "source": {"index": 15, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 174}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php&line=174", "ajax": false, "filename": "QueryDataTable.php", "line": "174"}, "connection": "adawliahshop", "explain": null, "start_percent": 1.46, "width_percent": 26.113}, {"sql": "select `ec_products`.`id`, `ec_products`.`name`, `ec_products`.`image`, `ec_products`.`images`, `ec_products`.`sku`, `ec_products`.`is_variation`, `pv`.`configurable_product_id` as `parent_product_id`, `va`.`variation_attributes`, `vc`.`variations_count`, `ec_products`.`stock_status`, `ec_products`.`quantity`, `ec_products`.`with_storehouse_management` from `ec_products` left join `ec_product_variations` as `pv` on `ec_products`.`id` = `pv`.`product_id` and `ec_products`.`is_variation` = 1 left join (select `pv`.`product_id`, GROUP_CONCAT(CONCAT(pas.title, ': ', pa.title) ORDER BY pas.order, pa.order SEPARATOR ', ') as variation_attributes from `ec_product_variations` as `pv` left join `ec_product_variation_items` as `pvi` on `pvi`.`variation_id` = `pv`.`id` left join `ec_product_attributes` as `pa` on `pa`.`id` = `pvi`.`attribute_id` left join `ec_product_attribute_sets` as `pas` on `pas`.`id` = `pa`.`attribute_set_id` group by `pv`.`product_id`) as `va` on `ec_products`.`id` = `va`.`product_id` left join (select `configurable_product_id`, COUNT(*) as variations_count from `ec_product_variations` group by `configurable_product_id`) as `vc` on `ec_products`.`id` = `vc`.`configurable_product_id` order by `ec_products`.`name` asc, `parent_product_id` asc limit 100 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 146}, {"index": 14, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 129}, {"index": 15, "namespace": null, "name": "vendor/botble/platform/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 920}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/ProductBulkEditableTable.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Tables\\ProductBulkEditableTable.php", "line": 81}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3991141, "duration": 0.060520000000000004, "duration_str": "60.52ms", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:146", "source": {"index": 13, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 146}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php&line=146", "ajax": false, "filename": "QueryDataTable.php", "line": "146"}, "connection": "adawliahshop", "explain": null, "start_percent": 27.573, "width_percent": 72.427}]}, "models": {"data": {"Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://adawliahshop.gc/admin/ecommerce/product-inventory", "action_name": "ecommerce.product-inventory.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductInventoryController@index", "uri": "GET admin/ecommerce/product-inventory", "permission": "ecommerce.product-inventory.index", "controller": "Botble\\Ecommerce\\Http\\Controllers\\ProductInventoryController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductInventoryController.php&line=14\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/product-inventory", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductInventoryController.php&line=14\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/ProductInventoryController.php:14-21</a>", "middleware": "web, core, auth", "duration": "2.25s", "peak_memory": "66MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-171210860 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-171210860\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-600898896 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"26 characters\">with_storehouse_management</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">with_storehouse_management</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600898896\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2015154930 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">adawliahshop.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1248</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://adawliahshop.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">https://adawliahshop.gc/admin/ecommerce/product-inventory</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2673 characters\">botble_footprints_cookie=eyJpdiI6InBYMHJNeEtkbHFlbWpBZU5QVnQrYlE9PSIsInZhbHVlIjoiM0JxSk5CTXlleEZZMzNCb3h5cUdTVXBBajFKTVI0VFZxemc3eWtvNWdoZHF2bUdRbkZUM2hscVkvcWk1Q2RJRVRTVDBGNmZjVEpZNFozWFU4K0RsL09Fby9BZGx3NGRHdjdwbDFZWDg4YmJZUkpmSmJXa0EwSlRJUWhmTEdqY3UiLCJtYWMiOiI3ZDRiNDUwNGFkYzNlOWZhYTNkNTNkOTlkYTMwYTkxY2U0MTAxMzJlZDFlMzNjOGJjZTZjYWFlMGI2ZGRmMzNiIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IjBNcGkyazd4dExDbnlaSmNmUnpOd0E9PSIsInZhbHVlIjoiMUtFOGhLckFUdnpGWVRyekl0MVpCVTZSKytUYitPNk5LR3oxTUdTbDdHZUt5MmtWS1ZJV2d0NFNNY1FDeWhnenJ4VDhRQk9BMDRqNklkZXU5UlNHZ1dBdStUUThWZHl2dkJzUDh2V09HMDRwa0NCY0NsTmt3NGV4MVlBWmhmRDUvbnpKOGZXcnU4aHJMWjZqVnB3akdLWGxsWmlHNVpSSVFhUFIvbTM2UE5IY01IVDhUMTlLN3lST1RoWW1iYVFQeTdpdjBsYjZrUTl2eE5oSnJLMU8rZUFqRjVDaVZUYTRBamxBM3phMUd2SlZTOGVyNXh0NzNxeVdXdmU0N0pLZGU0OVJvSUU4YXg4ZmM3dDVsMkRwSkx4RjN1NWN4aW1QbS9QSGJLdjlUdktvRW1UR1BIT3hqYUZSRy84YnZGQmxRT3VpZ2JsSGlGZDM2MmF3KzdzMms2VEJ0UVhZSE0vTzZKWU5pQ3JKYUhhbXhKc0dxK056dWZHTkp3QlZ6bW1LRFpxcmlkeGpXMHpoNG5uS0hkSitQWlhPTnJWb1Z4TDZXcDJFNmNFVTZjcW5DNmMwQUd3QWhoUVMySGpIZUkrRWxlamNVOC9KYzl1ZlN6aWUrSTdHWlZGYVorZGkwVFo4Nys1UG5IcDdFcjBYSmE3anRRQXhGcWdZNzlQQWpCVTFpZDh3RHJMaG1EZHRkVXpoUFVUNlhnPT0iLCJtYWMiOiIxZTdkY2JiZTI5NWNmMjdkZGQ5YThhYjdlYmUwMzk4NWZjZDkzYzJiNmYwZDFhMmE5ZDA3NDdiZmQzY2E0MjBhIiwidGFnIjoiIn0%3D; perf_dv6Tr4n=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkpIaFpaMEp5SFBNQ0pmZisvZStlaFE9PSIsInZhbHVlIjoiVXJHbWNMUmptbk5paVZvK0ZHUHNDaDFVQWp3L1NOMEJVdWJnWVFKWHhHWWJ5ZlF5TkpZRE1sWGRBRk85bUtUek9xVUc0cHV3cEkzYUVLbzgzQTVvUUJwV2NBaGQ0R3ROUG9xN3hodUxicG9XUS9OZlE1dVVIRXBQaDg4REF4NEpHWjhrZ0Z4Mk9iV293d1JDd0p0YXR4ejNDZTdCNEc3SGRSUWpUQ3JLUmtlcUx5S250TWI3ajY5SkFqRFgvOFMxY3RRVjFQdEtKR3ppVGRnaVg2dVpKUXdWZ0NzVndkdDVqZU9la2FGdmFDcz0iLCJtYWMiOiJmMjllOGFmY2RjOGE0OTQ2ZjdjMjdlMDVmMjFhZWY5ZDA2NTcxNTkzNDA4NmU1OGNlZWNlZjJkM2VkOGE4M2U0IiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; cookie_for_consent={&quot;essential&quot;:true}; XSRF-TOKEN=eyJpdiI6IjNwN3llQ2VGNUNTT3FhZ3lwNE9ydmc9PSIsInZhbHVlIjoiNHVrVktWcmpnMmZXL0piaTNGcU42QUJyR2lHR1RQS3hiZjM3ejFwUlppendBeXRodGpuNmNwNWxPendubnZMbTZzVTVWV2N3MTJFYWpWKytLRjEvMzdmTVA0RXBJN2VoZHdJZVNGQnVvOWEwV2x6UDBvSWZlV0NyTFU3R2dtM1YiLCJtYWMiOiIwYzRmZjRjOWFiMzBhMjZiNDQ3NjU3N2NiMDEwODFhYjVjOTg0MWVkYzdlZWU4N2IxYWU3MGRiYmIzY2NmYjZkIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlNlazZzTkhJN3gzS0NVTnhpMnoxNVE9PSIsInZhbHVlIjoiVm1TZ1VoNlc2UEsvN1BCK1JMUDNJUVdCWjRKZWxMNmtGM0xFV3prdWFxM3V6Qi9NU3R0WjA2ZmZwWlkyOWRSbm5XVzk2RzlFcUJ4ZlJNVE5qUG9TUjQ0Zm12cUg3NWJROGlyUjhHejlZb05BakZ6WW5ReWRreXpQOWE3bkNybEIiLCJtYWMiOiI1ZDUxZWU1MWU2MGUyNzQwOGMyZTY5NmY1MDZmYzAxYTA1ZDE3YzUwNzk3MGUxNzQ0Y2FiYzFhMzFiYzMwZmE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015154930\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1075869190 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b611e49ca9c9e01d12f55b9fdbec097ba769ddeb</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;footprint&quot;:&quot;b611e49ca9c9e01d12f55b9fdbec097ba769ddeb&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;adawliahshop.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str title=\"18 characters\">{&quot;essential&quot;:true}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOi7kX3uphlBCTLAiO3znuue74DN3GeKZVAdyGvz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075869190\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-925296539 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 01 Sep 2025 17:05:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925296539\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1894230652 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>5</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>+</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"57 characters\">https://adawliahshop.gc/admin/ecommerce/product-inventory</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>87</span> => <span class=sf-dump-num>1756743776</span>\n    <span class=sf-dump-key>11</span> => <span class=sf-dump-num>1756743874</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>recently_viewed_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4350</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010fe0000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:41:08.701823 from now\nDST Off\">2025-09-01 16:24:34.900653 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>recently_viewed</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4351</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>10bfb15bf3664801511d8fd701d75ab4</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4352</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">10bfb15bf3664801511d8fd701d75ab4</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>87</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">Camera</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>150.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4353</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: \"<span class=sf-dump-str title=\"31 characters\">Botble\\Ecommerce\\Models\\Product</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743777\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743777</span></span> {<a class=sf-dump-ref>#4354</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011020000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:42:46.417153 from now\nDST Off\">2025-09-01 16:22:57.185962 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743777\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743777</span></span> {<a class=sf-dump-ref>#4355</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011030000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:42:46.417241 from now\nDST Off\">2025-09-01 16:22:57.185954 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n        \"<span class=sf-dump-key>620d670d95f0419e35f9182695918c68</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4356</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">620d670d95f0419e35f9182695918c68</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>11</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"40 characters\">Xbox One Wireless Controller Black Color</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>1130.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4357</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: \"<span class=sf-dump-str title=\"31 characters\">Botble\\Ecommerce\\Models\\Product</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4358</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011060000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:41:08.702659 from now\nDST Off\">2025-09-01 16:24:34.900621 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4359</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011070000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:41:08.70273 from now\nDST Off\">2025-09-01 16:24:34.900614 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743840\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743840</span></span> {<a class=sf-dump-ref>#4360</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011080000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:41:43.317142 from now\nDST Off\">2025-09-01 16:24:00.285438 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ac3ad28e5760b4699bfcd47a50c6957d</span>\"\n  \"<span class=sf-dump-key>07df26edabf1a8846957c43ccff73a8c</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894230652\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://adawliahshop.gc/admin/ecommerce/product-inventory", "action_name": "ecommerce.product-inventory.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductInventoryController@index"}, "badge": null}}