<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('ec_order_product', function (Blueprint $table): void {
            $table->foreignId('branch_id')->nullable()->after('product_id')->constrained('branches')->onDelete('set null');
            $table->index(['branch_id', 'product_id']);
        });
    }

    public function down(): void
    {
        Schema::table('ec_order_product', function (Blueprint $table): void {
            $table->dropForeign(['branch_id']);
            $table->dropIndex(['branch_id', 'product_id']);
            $table->dropColumn('branch_id');
        });
    }
};
