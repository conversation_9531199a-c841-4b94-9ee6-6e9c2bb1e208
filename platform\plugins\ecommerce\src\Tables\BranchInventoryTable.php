<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Tables;

use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Ecommerce\Models\BranchInventory;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\NumberBulkChange;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\FormattedColumn;
use Botble\Table\Columns\IdColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;

class BranchInventoryTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(BranchInventory::class)
            ->addColumns([
                IdColumn::make(),
                FormattedColumn::make('branch_name')
                    ->title(trans('Branch'))
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        return Html::link(
                            route('branch-management.branches.edit', $item->branch_id),
                            $item->branch->name ?? 'N/A',
                            ['target' => '_blank']
                        );
                    })
                    ->searchable(false)
                    ->orderable(false),
                FormattedColumn::make('product_name')
                    ->title(trans('Product'))
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        return Html::link(
                            route('products.edit', $item->product_id),
                            $item->product->name ?? 'N/A',
                            ['target' => '_blank']
                        );
                    })
                    ->searchable(false)
                    ->orderable(false),
                FormattedColumn::make('quantity')
                    ->title(trans('Stock Quantity'))
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        $badgeClass = 'badge-success';

                        if ($item->isOutOfStock()) {
                            $badgeClass = 'badge-danger';
                        } elseif ($item->isLowStock()) {
                            $badgeClass = 'badge-warning';
                        }

                        return view('plugins/ecommerce::branch-inventory.columns.quantity', [
                            'item' => $item,
                            'badgeClass' => $badgeClass,
                        ]);
                    })
                    ->orderable(true),
                FormattedColumn::make('min_stock_alert')
                    ->title(trans('Min Stock Alert'))
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();
                        return view('plugins/ecommerce::branch-inventory.columns.min-stock', [
                            'item' => $item,
                        ]);
                    })
                    ->orderable(true),
                FormattedColumn::make('status')
                    ->title(trans('Status'))
                    ->renderUsing(function (FormattedColumn $column) {
                        $item = $column->getItem();

                        if ($item->isOutOfStock()) {
                            return '<span class="badge badge-danger">Out of Stock</span>';
                        } elseif ($item->isLowStock()) {
                            return '<span class="badge badge-warning">Low Stock</span>';
                        }

                        return '<span class="badge badge-success">In Stock</span>';
                    })
                    ->searchable(false)
                    ->orderable(false),
                CreatedAtColumn::make(),
            ])
            ->addActions([
                EditAction::make()->route('ecommerce.branch-inventory.edit'),
                DeleteAction::make()->route('ecommerce.branch-inventory.destroy'),
            ])
            ->addBulkActions([
                DeleteBulkAction::make()->permission('ecommerce.branch-inventory.destroy'),
            ])
            ->addBulkChanges([
                NumberBulkChange::make()
                    ->name('quantity')
                    ->title(trans('Stock Quantity'))
                    ->validate('required|integer|min:0'),
                NumberBulkChange::make()
                    ->name('min_stock_alert')
                    ->title(trans('Min Stock Alert'))
                    ->validate('required|integer|min:0'),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->with(['branch', 'product'])
                    ->select([
                        'id',
                        'branch_id',
                        'product_id',
                        'quantity',
                        'min_stock_alert',
                        'created_at',
                    ]);
            })
            ->onAjax(function (): JsonResponse {
                return $this->toJson(
                    $this
                        ->table
                        ->eloquent($this->query())
                        ->editColumn('quantity', function (BranchInventory $item) {
                            return view('plugins/ecommerce::branch-inventory.columns.quantity', compact('item'));
                        })
                        ->editColumn('min_stock_alert', function (BranchInventory $item) {
                            return view('plugins/ecommerce::branch-inventory.columns.min-stock', compact('item'));
                        })
                        ->filter(function (Builder $query) {
                            $keyword = $this->request->input('search.value');
                            if ($keyword) {
                                return $query
                                    ->whereHas('branch', function (Builder $subQuery) use ($keyword) {
                                        return $subQuery->where('name', 'LIKE', "%{$keyword}%");
                                    })
                                    ->orWhereHas('product', function (Builder $subQuery) use ($keyword) {
                                        return $subQuery->where('name', 'LIKE', "%{$keyword}%");
                                    });
                            }

                            return $query;
                        })
                );
            });
    }
}
