<?php

namespace Bo<PERSON><PERSON>\Ecommerce\Database\Seeders;

use Bo<PERSON>ble\Base\Supports\BaseSeeder;
use Bo<PERSON>ble\Ecommerce\Models\BranchInventory;
use Bo<PERSON>ble\Ecommerce\Models\Product;
use Bo<PERSON>ble\BranchManagement\Models\Branch;

class BranchInventorySeeder extends BaseSeeder
{
    public function run(): void
    {
        BranchInventory::query()->truncate();

        $branches = Branch::query()->get();
        $products = Product::query()->wherePublished()->limit(20)->get();

        if ($branches->isEmpty() || $products->isEmpty()) {
            $this->command->info('No branches or products found. Skipping branch inventory seeding.');
            return;
        }

        $this->command->info('Seeding branch inventory...');

        foreach ($products as $product) {
            foreach ($branches as $branch) {
                // Create random stock levels for each product at each branch
                $quantity = rand(0, 50);
                $minStockAlert = rand(3, 10);

                BranchInventory::create([
                    'branch_id' => $branch->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'min_stock_alert' => $minStockAlert,
                ]);
            }
        }

        $this->command->info('Branch inventory seeded successfully!');
    }
}
