
@if(get_ecommerce_setting('branch_management_enable_pickup', true))
@php
    $cities = \Botble\Location\Models\City::query()->wherePublished()->oldest('order')->oldest('name')->get();
@endphp

@if($cities->isNotEmpty())
<div class="product-branches-section mt-4" data-ajax-url="{{ route('api.branches.by-city') }}" data-product-id="{{ $product->id }}">
    <h5>{{ trans('plugins/branch-management::branch.pickup.title') }}</h5>
    <p class="text-muted">{{ __('Check availability and stock in your city') }}</p>

    <div class="row">
        <div class="col-md-6">
            <div class="form-group mb-3">
                <label for="product-city-select" class="form-label">{{ __('Select City') }}</label>
                <select id="product-city-select" class="form-control">
                    <option value="">{{ __('Select a city...') }}</option>
                    @foreach($cities as $city)
                        <option value="{{ $city->id }}">{{ $city->name }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group mb-3">
                <label for="product-branch-select" class="form-label">{{ __('Available Branches') }}</label>
                <select id="product-branch-select" class="form-control" disabled>
                    <option value="">{{ __('First select a city') }}</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Branches list with stock information -->
    <div id="product-branches-list" class="branches-list mt-3">
        <div class="alert alert-info">
            <i class="fa fa-info-circle"></i> {{ __('Please select a city to see available branches and stock information') }}
        </div>
    </div>

    <!-- Hidden field to store selected branch for cart -->
    <input type="hidden" id="selected-branch-id" name="branch_id" value="">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const citySelect = document.getElementById('product-city-select');
    const branchSelect = document.getElementById('product-branch-select');

    if (citySelect && branchSelect) {
        citySelect.addEventListener('change', function() {
            const cityId = this.value;

            if (!cityId) {
                branchSelect.innerHTML = '<option value="">{{ __('First select a city') }}</option>';
                branchSelect.disabled = true;
                return;
            }

            branchSelect.innerHTML = '<option value="">{{ __('Loading...') }}</option>';
            branchSelect.disabled = true;

            fetch(`{{ route('public.ajax.branches-by-city') }}?city_id=${cityId}`)
                .then(response => response.json())
                .then(branches => {
                    branchSelect.innerHTML = '<option value="">{{ __('Select a branch...') }}</option>';

                    if (branches.length > 0) {
                        branches.forEach(branch => {
                            const option = document.createElement('option');
                            option.value = branch.id;
                            option.textContent = branch.name + ' - ' + branch.address;
                            branchSelect.appendChild(option);
                        });
                        branchSelect.disabled = false;
                    } else {
                        branchSelect.innerHTML = '<option value="">{{ __('No branches in this city') }}</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading branches:', error);
                    branchSelect.innerHTML = '<option value="">{{ __('Error loading branches') }}</option>';
                });
        });
    }
});
</script>
@endif
@endif


