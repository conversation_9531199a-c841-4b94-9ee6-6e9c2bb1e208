<?php

return [
    'name' => 'Branch Inventory',
    'branch_inventory' => 'Branch Inventory',
    'manage_branch_inventory' => 'Manage Branch Inventory',
    'branch' => 'Branch',
    'product' => 'Product',
    'quantity' => 'Quantity',
    'min_stock_alert' => 'Min Stock Alert',
    'status' => 'Status',
    'in_stock' => 'In Stock',
    'low_stock' => 'Low Stock',
    'out_of_stock' => 'Out of Stock',
    'available' => 'Available',
    'not_available' => 'Not Available',
    
    // Actions
    'update_stock' => 'Update Stock',
    'bulk_update' => 'Bulk Update',
    'transfer_stock' => 'Transfer Stock',
    'initialize_product' => 'Initialize Product',
    'add_stock' => 'Add Stock',
    'reduce_stock' => 'Reduce Stock',
    'set_stock' => 'Set Stock',
    
    // Filters
    'filter_by_branch' => 'Filter by Branch',
    'filter_by_product' => 'Filter by Product',
    'filter_by_status' => 'Filter by Status',
    'all_branches' => 'All Branches',
    'all_products' => 'All Products',
    'all_status' => 'All Status',
    
    // Forms
    'select_branch' => 'Select Branch',
    'select_product' => 'Select Product',
    'choose_branch' => 'Choose Branch',
    'choose_product' => 'Choose Product',
    'from_branch' => 'From Branch',
    'to_branch' => 'To Branch',
    'default_quantity' => 'Default Quantity',
    'transfer_quantity' => 'Transfer Quantity',
    'available_stock' => 'Available Stock',
    
    // Messages
    'stock_updated_successfully' => 'Stock updated successfully',
    'stock_transferred_successfully' => 'Stock transferred successfully',
    'product_initialized_successfully' => 'Product inventory initialized successfully',
    'bulk_update_completed' => 'Bulk update completed successfully',
    'insufficient_stock' => 'Insufficient stock available',
    'invalid_quantity' => 'Invalid quantity specified',
    'no_inventory_record' => 'No inventory record found',
    'transfer_same_branch_error' => 'Cannot transfer to the same branch',
    
    // CSV Upload
    'upload_csv' => 'Upload CSV File',
    'csv_format' => 'CSV format: product_id,quantity',
    'products_to_update' => 'Products to Update',
    
    // Modals
    'bulk_update_modal_title' => 'Bulk Update Stock',
    'transfer_modal_title' => 'Transfer Stock',
    'initialize_modal_title' => 'Initialize Product Inventory',
    'cancel' => 'Cancel',
    'update' => 'Update',
    'transfer' => 'Transfer',
    'initialize' => 'Initialize',
    
    // Descriptions
    'bulk_update_description' => 'Update stock quantities for multiple products at once',
    'transfer_description' => 'Transfer stock from one branch to another',
    'initialize_description' => 'Set up initial inventory for a product across all branches',
    'default_quantity_description' => 'This quantity will be set for all branches',
    
    // Stock Status
    'stock_status' => [
        'in_stock' => 'In Stock',
        'low_stock' => 'Low Stock',
        'out_of_stock' => 'Out of Stock',
    ],
    
    // Validation
    'validation' => [
        'branch_required' => 'Branch is required',
        'product_required' => 'Product is required',
        'quantity_required' => 'Quantity is required',
        'quantity_min' => 'Quantity must be at least 0',
        'transfer_quantity_min' => 'Transfer quantity must be at least 1',
        'different_branches' => 'Source and destination branches must be different',
    ],
    
    // Table Headers
    'table' => [
        'branch_name' => 'Branch Name',
        'product_name' => 'Product Name',
        'current_stock' => 'Current Stock',
        'min_alert' => 'Min Alert',
        'stock_status' => 'Stock Status',
        'last_updated' => 'Last Updated',
        'actions' => 'Actions',
    ],
    
    // Reports
    'reports' => [
        'low_stock_items' => 'Low Stock Items',
        'out_of_stock_items' => 'Out of Stock Items',
        'stock_summary' => 'Stock Summary',
        'branch_performance' => 'Branch Performance',
    ],
];
