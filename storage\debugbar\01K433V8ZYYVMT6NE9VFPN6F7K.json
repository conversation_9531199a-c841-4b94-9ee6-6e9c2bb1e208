{"__meta": {"id": "01K433V8ZYYVMT6NE9VFPN6F7K", "datetime": "2025-09-01 17:05:27", "utime": **********.039931, "method": "GET", "uri": "/admin/blog/posts/widgets/recent-posts", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 51, "start": 1756746321.79339, "end": **********.039954, "duration": 5.246563911437988, "duration_str": "5.25s", "measures": [{"label": "Booting", "start": 1756746321.79339, "relative_start": 0, "end": **********.453543, "relative_end": **********.453543, "duration": 1.****************, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.453561, "relative_start": 1.****************, "end": **********.039958, "relative_end": 4.0531158447265625e-06, "duration": 3.****************, "duration_str": "3.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.500913, "relative_start": 1.****************, "end": **********.517197, "relative_end": **********.517197, "duration": 0.*****************, "duration_str": "16.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/blog::widgets.posts", "start": **********.015819, "relative_start": 2.***************, "end": **********.015819, "relative_end": **********.015819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.header.cell", "start": **********.859783, "relative_start": 5.**************, "end": **********.859783, "relative_end": **********.859783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.header.cell", "start": **********.900196, "relative_start": 5.106806039810181, "end": **********.900196, "relative_end": **********.900196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.header.cell", "start": **********.900506, "relative_start": 5.107115983963013, "end": **********.900506, "relative_end": **********.900506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.header.index", "start": **********.900933, "relative_start": 5.107542991638184, "end": **********.900933, "relative_end": **********.900933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.939672, "relative_start": 5.146281957626343, "end": **********.939672, "relative_end": **********.939672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.94795, "relative_start": 5.154559850692749, "end": **********.94795, "relative_end": **********.94795, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.948735, "relative_start": 5.1553449630737305, "end": **********.948735, "relative_end": **********.948735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.949068, "relative_start": 5.155678033828735, "end": **********.949068, "relative_end": **********.949068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.949473, "relative_start": 5.15608286857605, "end": **********.949473, "relative_end": **********.949473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.957381, "relative_start": 5.1639909744262695, "end": **********.957381, "relative_end": **********.957381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.958333, "relative_start": 5.164942979812622, "end": **********.958333, "relative_end": **********.958333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.958801, "relative_start": 5.165410995483398, "end": **********.958801, "relative_end": **********.958801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.9596, "relative_start": 5.166209936141968, "end": **********.9596, "relative_end": **********.9596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.966492, "relative_start": 5.173101902008057, "end": **********.966492, "relative_end": **********.966492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.967159, "relative_start": 5.173768997192383, "end": **********.967159, "relative_end": **********.967159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.967458, "relative_start": 5.174067974090576, "end": **********.967458, "relative_end": **********.967458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.967839, "relative_start": 5.1744489669799805, "end": **********.967839, "relative_end": **********.967839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.97689, "relative_start": 5.183500051498413, "end": **********.97689, "relative_end": **********.97689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.97787, "relative_start": 5.1844799518585205, "end": **********.97787, "relative_end": **********.97787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.978206, "relative_start": 5.184815883636475, "end": **********.978206, "relative_end": **********.978206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.978944, "relative_start": 5.185554027557373, "end": **********.978944, "relative_end": **********.978944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.984638, "relative_start": 5.191247940063477, "end": **********.984638, "relative_end": **********.984638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.985042, "relative_start": 5.191652059555054, "end": **********.985042, "relative_end": **********.985042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.985218, "relative_start": 5.191828012466431, "end": **********.985218, "relative_end": **********.985218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.985441, "relative_start": 5.192050933837891, "end": **********.985441, "relative_end": **********.985441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.989168, "relative_start": 5.195777893066406, "end": **********.989168, "relative_end": **********.989168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.989494, "relative_start": 5.196104049682617, "end": **********.989494, "relative_end": **********.989494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.989668, "relative_start": 5.196277856826782, "end": **********.989668, "relative_end": **********.989668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.989883, "relative_start": 5.196492910385132, "end": **********.989883, "relative_end": **********.989883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.998074, "relative_start": 5.204684019088745, "end": **********.998074, "relative_end": **********.998074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.998912, "relative_start": 5.205522060394287, "end": **********.998912, "relative_end": **********.998912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.999257, "relative_start": 5.205867052078247, "end": **********.999257, "relative_end": **********.999257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.999664, "relative_start": 5.206274032592773, "end": **********.999664, "relative_end": **********.999664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.008221, "relative_start": 5.2148308753967285, "end": **********.008221, "relative_end": **********.008221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.008945, "relative_start": 5.21555495262146, "end": **********.008945, "relative_end": **********.008945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.009284, "relative_start": 5.215893983840942, "end": **********.009284, "relative_end": **********.009284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.009726, "relative_start": 5.216336011886597, "end": **********.009726, "relative_end": **********.009726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.019363, "relative_start": 5.225972890853882, "end": **********.019363, "relative_end": **********.019363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.020028, "relative_start": 5.226638078689575, "end": **********.020028, "relative_end": **********.020028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.020355, "relative_start": 5.226964950561523, "end": **********.020355, "relative_end": **********.020355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.020738, "relative_start": 5.2273478507995605, "end": **********.020738, "relative_end": **********.020738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.031255, "relative_start": 5.237864971160889, "end": **********.031255, "relative_end": **********.031255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.cell", "start": **********.03197, "relative_start": 5.238579988479614, "end": **********.03197, "relative_end": **********.03197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.row", "start": **********.032241, "relative_start": 5.238851070404053, "end": **********.032241, "relative_end": **********.032241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.body.index", "start": **********.032653, "relative_start": 5.23926305770874, "end": **********.032653, "relative_end": **********.032653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::table.index", "start": **********.033168, "relative_start": 5.2397780418396, "end": **********.033168, "relative_end": **********.033168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.03394, "relative_start": 5.2405500411987305, "end": **********.036462, "relative_end": **********.036462, "duration": 0.002521991729736328, "duration_str": "2.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 52662664, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "adawliahshop.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 47, "nb_templates": 47, "templates": [{"name": "plugins/blog::widgets.posts", "param_count": null, "params": [], "start": **********.015765, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/plugins/blog/resources/views/widgets/posts.blade.phpplugins/blog::widgets.posts", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fblog%2Fresources%2Fviews%2Fwidgets%2Fposts.blade.php&line=1", "ajax": false, "filename": "posts.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.header.cell", "param_count": null, "params": [], "start": **********.859725, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/header/cell.blade.php8def1252668913628243c4d363bee1ef::table.header.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.header.cell", "param_count": null, "params": [], "start": **********.900171, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/header/cell.blade.php8def1252668913628243c4d363bee1ef::table.header.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.header.cell", "param_count": null, "params": [], "start": **********.900485, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/header/cell.blade.php8def1252668913628243c4d363bee1ef::table.header.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.header.index", "param_count": null, "params": [], "start": **********.900894, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/header/index.blade.php8def1252668913628243c4d363bee1ef::table.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.939645, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.947915, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.948706, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.949039, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.949446, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.957342, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.958272, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.95876, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.959567, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.966468, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.967124, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.967424, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.967806, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.976834, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.977821, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.978165, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.978901, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.984615, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.985022, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.985199, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.985422, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.989147, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.989475, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.989649, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.989863, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.998028, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.998873, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.999222, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.99963, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.008177, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.008912, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.009251, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.009691, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.01932, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.019993, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.020321, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.020705, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.031216, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.cell", "param_count": null, "params": [], "start": **********.03194, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/cell.blade.php8def1252668913628243c4d363bee1ef::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.row", "param_count": null, "params": [], "start": **********.032211, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/row.blade.php8def1252668913628243c4d363bee1ef::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.body.index", "param_count": null, "params": [], "start": **********.032622, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/body/index.blade.php8def1252668913628243c4d363bee1ef::table.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "8def1252668913628243c4d363bee1ef::table.index", "param_count": null, "params": [], "start": **********.033137, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/table/index.blade.php8def1252668913628243c4d363bee1ef::table.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.38273, "accumulated_duration_str": "383ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.566235, "duration": 0.18743, "duration_str": "187ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 0, "width_percent": 48.972}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.7678761, "duration": 0.0684, "duration_str": "68.4ms", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "adawliahshop", "explain": null, "start_percent": 48.972, "width_percent": 17.872}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.8484771, "duration": 0.01572, "duration_str": "15.72ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 66.843, "width_percent": 4.107}, {"sql": "select * from `posts` order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/blog/src/Http/Controllers/PostController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\blog\\src\\Http\\Controllers\\PostController.php", "line": 105}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.878209, "duration": 0.*****************, "duration_str": "109ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 70.951, "width_percent": 28.469}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (2, 3, 4, 5, 6, 7, 8, 9, 10, 11) and `slugs`.`reference_type` = 'Botble\\\\Blog\\\\Models\\\\Post'", "type": "query", "params": [], "bindings": ["Botble\\Blog\\Models\\Post"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/blog/src/Http/Controllers/PostController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\blog\\src\\Http\\Controllers\\PostController.php", "line": 105}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0005932, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 99.42, "width_percent": 0.58}]}, "models": {"data": {"Botble\\Blog\\Models\\Post": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fblog%2Fsrc%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 22, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 22}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://adawliahshop.gc/admin/blog/posts/widgets/recent-posts", "action_name": "posts.widget.recent-posts", "controller_action": "Botble\\Blog\\Http\\Controllers\\PostController@getWidgetRecentPosts", "uri": "GET admin/blog/posts/widgets/recent-posts", "permission": "posts.index", "controller": "Botble\\Blog\\Http\\Controllers\\PostController@getWidgetRecentPosts<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fblog%2Fsrc%2FHttp%2FControllers%2FPostController.php&line=97\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Blog\\Http\\Controllers", "prefix": "admin/blog/posts", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fblog%2Fsrc%2FHttp%2FControllers%2FPostController.php&line=97\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/blog/src/Http/Controllers/PostController.php:97-110</a>", "middleware": "web, core, auth", "duration": "5.24s", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1214419307 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1214419307\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-697543557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-697543557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2091302895 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImxNQkk0Y2FPbXNTYnREaDlTdk5NSUE9PSIsInZhbHVlIjoiWkEyWUUzc2Z4SHpWUE1iaU53eHBUT1dETWNOdjhVaUpqMTVwZnpMVE1JQmIybDRyVmk0Ri9jK1BVWXdUdUFBWnFEUXFIYjJ1L3JDbkxOeTd6cnBYaGhjem5NTUpaUzQwaXVwUUxVaXJpZ1ZyOGdCcDFVQ01xUVVmTmpjN0U4N04iLCJtYWMiOiIxNmFmZWI3NzE5MDAxYTEwZTVhZTllOGQwMzhkMDU5MDM2NWViN2M4N2Q0MzAwN2YwMGQ3MTE0MDNkMjZkZTA4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">adawliahshop.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">https://adawliahshop.gc/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2673 characters\">botble_footprints_cookie=eyJpdiI6InBYMHJNeEtkbHFlbWpBZU5QVnQrYlE9PSIsInZhbHVlIjoiM0JxSk5CTXlleEZZMzNCb3h5cUdTVXBBajFKTVI0VFZxemc3eWtvNWdoZHF2bUdRbkZUM2hscVkvcWk1Q2RJRVRTVDBGNmZjVEpZNFozWFU4K0RsL09Fby9BZGx3NGRHdjdwbDFZWDg4YmJZUkpmSmJXa0EwSlRJUWhmTEdqY3UiLCJtYWMiOiI3ZDRiNDUwNGFkYzNlOWZhYTNkNTNkOTlkYTMwYTkxY2U0MTAxMzJlZDFlMzNjOGJjZTZjYWFlMGI2ZGRmMzNiIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IjBNcGkyazd4dExDbnlaSmNmUnpOd0E9PSIsInZhbHVlIjoiMUtFOGhLckFUdnpGWVRyekl0MVpCVTZSKytUYitPNk5LR3oxTUdTbDdHZUt5MmtWS1ZJV2d0NFNNY1FDeWhnenJ4VDhRQk9BMDRqNklkZXU5UlNHZ1dBdStUUThWZHl2dkJzUDh2V09HMDRwa0NCY0NsTmt3NGV4MVlBWmhmRDUvbnpKOGZXcnU4aHJMWjZqVnB3akdLWGxsWmlHNVpSSVFhUFIvbTM2UE5IY01IVDhUMTlLN3lST1RoWW1iYVFQeTdpdjBsYjZrUTl2eE5oSnJLMU8rZUFqRjVDaVZUYTRBamxBM3phMUd2SlZTOGVyNXh0NzNxeVdXdmU0N0pLZGU0OVJvSUU4YXg4ZmM3dDVsMkRwSkx4RjN1NWN4aW1QbS9QSGJLdjlUdktvRW1UR1BIT3hqYUZSRy84YnZGQmxRT3VpZ2JsSGlGZDM2MmF3KzdzMms2VEJ0UVhZSE0vTzZKWU5pQ3JKYUhhbXhKc0dxK056dWZHTkp3QlZ6bW1LRFpxcmlkeGpXMHpoNG5uS0hkSitQWlhPTnJWb1Z4TDZXcDJFNmNFVTZjcW5DNmMwQUd3QWhoUVMySGpIZUkrRWxlamNVOC9KYzl1ZlN6aWUrSTdHWlZGYVorZGkwVFo4Nys1UG5IcDdFcjBYSmE3anRRQXhGcWdZNzlQQWpCVTFpZDh3RHJMaG1EZHRkVXpoUFVUNlhnPT0iLCJtYWMiOiIxZTdkY2JiZTI5NWNmMjdkZGQ5YThhYjdlYmUwMzk4NWZjZDkzYzJiNmYwZDFhMmE5ZDA3NDdiZmQzY2E0MjBhIiwidGFnIjoiIn0%3D; perf_dv6Tr4n=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkpIaFpaMEp5SFBNQ0pmZisvZStlaFE9PSIsInZhbHVlIjoiVXJHbWNMUmptbk5paVZvK0ZHUHNDaDFVQWp3L1NOMEJVdWJnWVFKWHhHWWJ5ZlF5TkpZRE1sWGRBRk85bUtUek9xVUc0cHV3cEkzYUVLbzgzQTVvUUJwV2NBaGQ0R3ROUG9xN3hodUxicG9XUS9OZlE1dVVIRXBQaDg4REF4NEpHWjhrZ0Z4Mk9iV293d1JDd0p0YXR4ejNDZTdCNEc3SGRSUWpUQ3JLUmtlcUx5S250TWI3ajY5SkFqRFgvOFMxY3RRVjFQdEtKR3ppVGRnaVg2dVpKUXdWZ0NzVndkdDVqZU9la2FGdmFDcz0iLCJtYWMiOiJmMjllOGFmY2RjOGE0OTQ2ZjdjMjdlMDVmMjFhZWY5ZDA2NTcxNTkzNDA4NmU1OGNlZWNlZjJkM2VkOGE4M2U0IiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; cookie_for_consent={&quot;essential&quot;:true}; XSRF-TOKEN=eyJpdiI6ImxNQkk0Y2FPbXNTYnREaDlTdk5NSUE9PSIsInZhbHVlIjoiWkEyWUUzc2Z4SHpWUE1iaU53eHBUT1dETWNOdjhVaUpqMTVwZnpMVE1JQmIybDRyVmk0Ri9jK1BVWXdUdUFBWnFEUXFIYjJ1L3JDbkxOeTd6cnBYaGhjem5NTUpaUzQwaXVwUUxVaXJpZ1ZyOGdCcDFVQ01xUVVmTmpjN0U4N04iLCJtYWMiOiIxNmFmZWI3NzE5MDAxYTEwZTVhZTllOGQwMzhkMDU5MDM2NWViN2M4N2Q0MzAwN2YwMGQ3MTE0MDNkMjZkZTA4IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlJ4ZDJ2WXF4cDVITWt1SXZ2TVNnNnc9PSIsInZhbHVlIjoiZ0dwYVZxdCtGN0ZkNHdLZENzU2VUQUNodWZZdVRLbVpmNW1GMi81R0lzdVExSUozb2Z6ZTBvWHMyWWdtT0FjNHpCSC93YTArYmJyVUVNVFJCNHRYVW1ac2VSblBzWjJST2FNTys2UFVNNEphMVVjbDE5Y1R3VTNwckhwSEhJOGciLCJtYWMiOiI5NDZhZjQ0ZmMwMGNlNDBlZWUyYzVhMTYzMmExZTJjNjU4MGNjMWJkMjgyODIzNThiOWQ0NDRiNDY2N2E1ZmUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091302895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-822291587 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b611e49ca9c9e01d12f55b9fdbec097ba769ddeb</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;footprint&quot;:&quot;b611e49ca9c9e01d12f55b9fdbec097ba769ddeb&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;adawliahshop.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str title=\"18 characters\">{&quot;essential&quot;:true}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOi7kX3uphlBCTLAiO3znuue74DN3GeKZVAdyGvz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822291587\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1810169360 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 01 Sep 2025 17:05:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810169360\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-665407316 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>5</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>+</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">https://adawliahshop.gc/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>87</span> => <span class=sf-dump-num>1756743776</span>\n    <span class=sf-dump-key>11</span> => <span class=sf-dump-num>1756743874</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>recently_viewed_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4300</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010cc0000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:40:52.150863 from now\nDST Off\">2025-09-01 16:24:34.900653 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>recently_viewed</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4301</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>10bfb15bf3664801511d8fd701d75ab4</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4302</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">10bfb15bf3664801511d8fd701d75ab4</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>87</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">Camera</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>150.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4303</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: \"<span class=sf-dump-str title=\"31 characters\">Botble\\Ecommerce\\Models\\Product</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743777\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743777</span></span> {<a class=sf-dump-ref>#4304</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010d00000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:42:29.866551 from now\nDST Off\">2025-09-01 16:22:57.185962 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743777\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743777</span></span> {<a class=sf-dump-ref>#4305</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010d10000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:42:29.866651 from now\nDST Off\">2025-09-01 16:22:57.185954 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n        \"<span class=sf-dump-key>620d670d95f0419e35f9182695918c68</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4306</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">620d670d95f0419e35f9182695918c68</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>11</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"40 characters\">Xbox One Wireless Controller Black Color</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>1130.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4307</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: \"<span class=sf-dump-str title=\"31 characters\">Botble\\Ecommerce\\Models\\Product</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4308</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010d40000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:40:52.152071 from now\nDST Off\">2025-09-01 16:24:34.900621 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4309</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010d50000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:40:52.152139 from now\nDST Off\">2025-09-01 16:24:34.900614 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743840\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743840</span></span> {<a class=sf-dump-ref>#4310</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000010d60000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:41:26.766302 from now\nDST Off\">2025-09-01 16:24:00.285438 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ac3ad28e5760b4699bfcd47a50c6957d</span>\"\n  \"<span class=sf-dump-key>07df26edabf1a8846957c43ccff73a8c</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665407316\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://adawliahshop.gc/admin/blog/posts/widgets/recent-posts", "action_name": "posts.widget.recent-posts", "controller_action": "Botble\\Blog\\Http\\Controllers\\PostController@getWidgetRecentPosts"}, "badge": null}}