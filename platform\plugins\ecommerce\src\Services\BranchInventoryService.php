<?php

namespace Bo<PERSON>ble\Ecommerce\Services;

use Botble\Ecommerce\Models\BranchInventory;
use Bo<PERSON>ble\Ecommerce\Models\Product;
use Bo<PERSON>ble\BranchManagement\Models\Branch;
use Illuminate\Support\Facades\DB;
use Exception;

class BranchInventoryService
{
    /**
     * Add stock to a branch for a product
     */
    public function addStock(int $branchId, int $productId, int $quantity): BranchInventory
    {
        return DB::transaction(function () use ($branchId, $productId, $quantity) {
            $branchInventory = BranchInventory::firstOrCreate(
                ['branch_id' => $branchId, 'product_id' => $productId],
                ['quantity' => 0, 'min_stock_alert' => 5]
            );

            $branchInventory->quantity += $quantity;
            $branchInventory->save();

            return $branchInventory;
        });
    }

    /**
     * Reduce stock from a branch for a product
     */
    public function reduceStock(int $branchId, int $productId, int $quantity): BranchInventory
    {
        return DB::transaction(function () use ($branchId, $productId, $quantity) {
            $branchInventory = BranchInventory::where('branch_id', $branchId)
                ->where('product_id', $productId)
                ->lockForUpdate()
                ->first();

            if (!$branchInventory) {
                throw new Exception("No inventory record found for product {$productId} at branch {$branchId}");
            }

            if ($branchInventory->quantity < $quantity) {
                throw new Exception("Insufficient stock. Available: {$branchInventory->quantity}, Required: {$quantity}");
            }

            $branchInventory->quantity -= $quantity;
            $branchInventory->save();

            return $branchInventory;
        });
    }

    /**
     * Set stock quantity for a branch and product
     */
    public function setStock(int $branchId, int $productId, int $quantity): BranchInventory
    {
        return DB::transaction(function () use ($branchId, $productId, $quantity) {
            $branchInventory = BranchInventory::updateOrCreate(
                ['branch_id' => $branchId, 'product_id' => $productId],
                ['quantity' => $quantity]
            );

            return $branchInventory;
        });
    }

    /**
     * Transfer stock between branches
     */
    public function transferStock(int $fromBranchId, int $toBranchId, int $productId, int $quantity): array
    {
        return DB::transaction(function () use ($fromBranchId, $toBranchId, $productId, $quantity) {
            // Reduce from source branch
            $fromInventory = $this->reduceStock($fromBranchId, $productId, $quantity);
            
            // Add to destination branch
            $toInventory = $this->addStock($toBranchId, $productId, $quantity);

            return [
                'from' => $fromInventory,
                'to' => $toInventory
            ];
        });
    }

    /**
     * Get stock levels for a product across all branches
     */
    public function getProductStockAcrossBranches(int $productId): array
    {
        $inventory = BranchInventory::with('branch')
            ->where('product_id', $productId)
            ->get();

        return $inventory->map(function ($item) {
            return [
                'branch_id' => $item->branch_id,
                'branch_name' => $item->branch->name,
                'quantity' => $item->quantity,
                'min_stock_alert' => $item->min_stock_alert,
                'is_low_stock' => $item->isLowStock(),
                'is_out_of_stock' => $item->isOutOfStock(),
            ];
        })->toArray();
    }

    /**
     * Get low stock items for a branch
     */
    public function getLowStockItems(int $branchId): array
    {
        return BranchInventory::with(['product', 'branch'])
            ->where('branch_id', $branchId)
            ->lowStock()
            ->get()
            ->map(function ($item) {
                return [
                    'product_id' => $item->product_id,
                    'product_name' => $item->product->name,
                    'quantity' => $item->quantity,
                    'min_stock_alert' => $item->min_stock_alert,
                ];
            })->toArray();
    }

    /**
     * Get out of stock items for a branch
     */
    public function getOutOfStockItems(int $branchId): array
    {
        return BranchInventory::with(['product', 'branch'])
            ->where('branch_id', $branchId)
            ->outOfStock()
            ->get()
            ->map(function ($item) {
                return [
                    'product_id' => $item->product_id,
                    'product_name' => $item->product->name,
                    'quantity' => $item->quantity,
                ];
            })->toArray();
    }

    /**
     * Initialize inventory for a product across all branches
     */
    public function initializeProductInventory(int $productId, int $defaultQuantity = 0): void
    {
        $branches = Branch::active()->get();
        
        foreach ($branches as $branch) {
            BranchInventory::firstOrCreate(
                ['branch_id' => $branch->id, 'product_id' => $productId],
                ['quantity' => $defaultQuantity, 'min_stock_alert' => 5]
            );
        }
    }

    /**
     * Bulk update stock for multiple products at a branch
     */
    public function bulkUpdateStock(int $branchId, array $stockData): void
    {
        DB::transaction(function () use ($branchId, $stockData) {
            foreach ($stockData as $productId => $quantity) {
                $this->setStock($branchId, $productId, $quantity);
            }
        });
    }
}
