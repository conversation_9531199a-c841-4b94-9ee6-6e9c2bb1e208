<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use Botble\Ecommerce\Http\Controllers\BranchInventoryController;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function () {
    Route::group(['namespace' => 'Botble\Ecommerce\Http\Controllers', 'prefix' => 'ecommerce'], function () {
        Route::group(['prefix' => 'branch-inventory', 'as' => 'ecommerce.branch-inventory.'], function () {
            Route::get('/', [BranchInventoryController::class, 'index'])->name('index');
            Route::post('/update', [BranchInventoryController::class, 'update'])->name('update');
            Route::post('/bulk-update', [BranchInventoryController::class, 'bulkUpdate'])->name('bulk-update');
            Route::post('/transfer', [BranchInventoryController::class, 'transfer'])->name('transfer');
            Route::post('/initialize', [BranchInventoryController::class, 'initializeProduct'])->name('initialize');
            Route::get('/product-stock', [BranchInventoryController::class, 'getProductStock'])->name('product-stock');
            Route::get('/branch-stock', [BranchInventoryController::class, 'getBranchStock'])->name('branch-stock');
        });
    });
});
