class BranchInventoryManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeSelect2();
    }

    initializeSelect2() {
        $('#branch-filter, #product-filter, #status-filter').select2({
            placeholder: 'Select an option',
            allowClear: true
        });

        $('#bulk-branch, #from-branch, #to-branch, #transfer-product, #init-product').select2({
            placeholder: 'Select an option',
            allowClear: true
        });
    }

    bindEvents() {
        // Inline quantity updates
        $(document).on('change', '.quantity-input', this.handleQuantityUpdate.bind(this));
        $(document).on('change', '.min-stock-input', this.handleMinStockUpdate.bind(this));

        // Filter events
        $('#branch-filter, #product-filter, #status-filter').on('change', this.handleFilterChange.bind(this));

        // Modal form submissions
        $('#bulk-update-form').on('submit', this.handleBulkUpdate.bind(this));
        $('#transfer-form').on('submit', this.handleTransfer.bind(this));
        $('#initialize-form').on('submit', this.handleInitialize.bind(this));

        // CSV file upload
        $('#csv-file').on('change', this.handleCSVUpload.bind(this));

        // Transfer form - check available stock
        $('#from-branch, #transfer-product').on('change', this.checkAvailableStock.bind(this));
    }

    handleQuantityUpdate(event) {
        const input = $(event.target);
        const branchId = input.data('branch-id');
        const productId = input.data('product-id');
        const quantity = parseInt(input.val());

        if (quantity < 0) {
            input.val(0);
            return;
        }

        this.updateStock(branchId, productId, quantity, 'quantity');
    }

    handleMinStockUpdate(event) {
        const input = $(event.target);
        const branchId = input.data('branch-id');
        const productId = input.data('product-id');
        const minStock = parseInt(input.val());

        if (minStock < 0) {
            input.val(0);
            return;
        }

        this.updateMinStock(branchId, productId, minStock);
    }

    updateStock(branchId, productId, quantity, field = 'quantity') {
        $.ajax({
            url: '/admin/ecommerce/branch-inventory/update',
            method: 'POST',
            data: {
                branch_id: branchId,
                product_id: productId,
                quantity: quantity,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.error) {
                    Botble.showError(response.message);
                    // Reload the table to reset values
                    window.LaravelDataTables['branch-inventory-table'].ajax.reload();
                } else {
                    Botble.showSuccess('Stock updated successfully');
                    // Update the badge color based on new quantity
                    this.updateQuantityBadge(branchId, productId, quantity);
                }
            },
            error: (xhr) => {
                Botble.showError('Failed to update stock');
                // Reload the table to reset values
                window.LaravelDataTables['branch-inventory-table'].ajax.reload();
            }
        });
    }

    updateMinStock(branchId, productId, minStock) {
        // This would be a separate endpoint for updating min stock
        // For now, we'll use the same update endpoint
        $.ajax({
            url: '/admin/ecommerce/branch-inventory/update-min-stock',
            method: 'POST',
            data: {
                branch_id: branchId,
                product_id: productId,
                min_stock_alert: minStock,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.error) {
                    Botble.showError(response.message);
                } else {
                    Botble.showSuccess('Min stock alert updated successfully');
                }
            },
            error: (xhr) => {
                Botble.showError('Failed to update min stock alert');
            }
        });
    }

    updateQuantityBadge(branchId, productId, quantity) {
        const input = $(`.quantity-input[data-branch-id="${branchId}"][data-product-id="${productId}"]`);
        const badge = input.siblings('.badge');
        
        badge.removeClass('badge-success badge-warning badge-danger');
        badge.text(quantity);
        
        if (quantity <= 0) {
            badge.addClass('badge-danger');
        } else if (quantity <= 5) { // Assuming default min stock is 5
            badge.addClass('badge-warning');
        } else {
            badge.addClass('badge-success');
        }
    }

    handleFilterChange() {
        const branchId = $('#branch-filter').val();
        const productId = $('#product-filter').val();
        const status = $('#status-filter').val();

        // Apply filters to DataTable
        const table = window.LaravelDataTables['branch-inventory-table'];
        
        if (branchId) {
            table.column(1).search(branchId);
        } else {
            table.column(1).search('');
        }

        if (productId) {
            table.column(2).search(productId);
        } else {
            table.column(2).search('');
        }

        if (status) {
            table.column(5).search(status);
        } else {
            table.column(5).search('');
        }

        table.draw();
    }

    handleBulkUpdate(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        
        $.ajax({
            url: '/admin/ecommerce/branch-inventory/bulk-update',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (response) => {
                if (response.error) {
                    Botble.showError(response.message);
                } else {
                    Botble.showSuccess('Bulk update completed successfully');
                    $('#bulkUpdateModal').modal('hide');
                    window.LaravelDataTables['branch-inventory-table'].ajax.reload();
                }
            },
            error: (xhr) => {
                Botble.showError('Failed to perform bulk update');
            }
        });
    }

    handleTransfer(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        
        $.ajax({
            url: '/admin/ecommerce/branch-inventory/transfer',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (response) => {
                if (response.error) {
                    Botble.showError(response.message);
                } else {
                    Botble.showSuccess('Stock transferred successfully');
                    $('#transferModal').modal('hide');
                    window.LaravelDataTables['branch-inventory-table'].ajax.reload();
                }
            },
            error: (xhr) => {
                Botble.showError('Failed to transfer stock');
            }
        });
    }

    handleInitialize(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        
        $.ajax({
            url: '/admin/ecommerce/branch-inventory/initialize',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (response) => {
                if (response.error) {
                    Botble.showError(response.message);
                } else {
                    Botble.showSuccess('Product inventory initialized successfully');
                    $('#initializeModal').modal('hide');
                    window.LaravelDataTables['branch-inventory-table'].ajax.reload();
                }
            },
            error: (xhr) => {
                Botble.showError('Failed to initialize product inventory');
            }
        });
    }

    handleCSVUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const products = [];

            for (let i = 1; i < lines.length; i++) { // Skip header
                const line = lines[i].trim();
                if (line) {
                    const [productId, quantity] = line.split(',');
                    if (productId && quantity !== undefined) {
                        products.push({
                            product_id: parseInt(productId),
                            quantity: parseInt(quantity)
                        });
                    }
                }
            }

            this.displayBulkProducts(products);
        };
        reader.readAsText(file);
    }

    displayBulkProducts(products) {
        const container = $('#bulk-products-container');
        const list = $('#bulk-products-list');
        
        list.empty();
        
        products.forEach(product => {
            list.append(`
                <div class="row mb-2">
                    <div class="col-6">Product ID: ${product.product_id}</div>
                    <div class="col-6">Quantity: ${product.quantity}</div>
                </div>
            `);
        });
        
        container.show();
    }

    checkAvailableStock() {
        const fromBranchId = $('#from-branch').val();
        const productId = $('#transfer-product').val();
        
        if (fromBranchId && productId) {
            $.ajax({
                url: '/admin/ecommerce/branch-inventory/get-stock',
                method: 'GET',
                data: {
                    branch_id: fromBranchId,
                    product_id: productId
                },
                success: (response) => {
                    const stock = response.data.find(item => item.product_id == productId);
                    if (stock) {
                        $('#available-stock').show().html(
                            `Available stock: <strong>${stock.quantity}</strong> units`
                        );
                        $('#transfer-quantity').attr('max', stock.quantity);
                    }
                },
                error: () => {
                    $('#available-stock').hide();
                }
            });
        } else {
            $('#available-stock').hide();
        }
    }
}

// Initialize when document is ready
$(document).ready(() => {
    new BranchInventoryManager();
});
