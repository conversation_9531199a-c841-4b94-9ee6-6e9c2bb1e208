{"__meta": {"id": "01K431CTX07S8S4BE0FJ9K0YCW", "datetime": "2025-09-01 16:22:36", "utime": **********.705716, "method": "POST", "uri": "/admin/ecommerce/products/edit/87", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.172753, "end": **********.705733, "duration": 1.532979965209961, "duration_str": "1.53s", "measures": [{"label": "Booting", "start": **********.172753, "relative_start": 0, "end": **********.959098, "relative_end": **********.959098, "duration": 0.****************, "duration_str": "786ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.95911, "relative_start": 0.****************, "end": **********.705736, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "747ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.976109, "relative_start": 0.****************, "end": **********.983894, "relative_end": **********.983894, "duration": 0.*****************, "duration_str": "7.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.700026, "relative_start": 1.****************, "end": **********.702731, "relative_end": **********.702731, "duration": 0.0027048587799072266, "duration_str": "2.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "adawliahshop.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10141000000000001, "accumulated_duration_str": "101ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.998278, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 0, "width_percent": 0.404}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.002242, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "adawliahshop", "explain": null, "start_percent": 0.404, "width_percent": 0.266}, {"sql": "select * from `ec_products` where `id` = '87' limit 1", "type": "query", "params": [], "bindings": ["87"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 966}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.007379, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 0.671, "width_percent": 0.454}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.012526, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 1.124, "width_percent": 0.227}, {"sql": "select count(*) as aggregate from `ec_product_categories` where `id` = '38'", "type": "query", "params": [], "bindings": ["38"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 993}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 964}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}], "start": **********.1678731, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "adawliahshop", "explain": null, "start_percent": 1.351, "width_percent": 0.483}, {"sql": "select count(*) as aggregate from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 87 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 37}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.171384, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:37", "source": {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=37", "ajax": false, "filename": "StoreProductService.php", "line": "37"}, "connection": "adawliahshop", "explain": null, "start_percent": 1.834, "width_percent": 0.878}, {"sql": "update `ec_products` set `status` = 'published', `stock_status` = 'in_stock', `ec_products`.`updated_at` = '2025-09-01 16:22:36' where `id` = 87", "type": "query", "params": [], "bindings": [{"value": "published", "label": "Published"}, {"value": "in_stock", "label": "In stock"}, "2025-09-01 16:22:36", 87], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 101}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.19469, "duration": 0.02106, "duration_str": "21.06ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:101", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=101", "ajax": false, "filename": "StoreProductService.php", "line": "101"}, "connection": "adawliahshop", "explain": null, "start_percent": 2.712, "width_percent": 20.767}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 87 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 156}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 101}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}], "start": **********.237188, "duration": 0.00505, "duration_str": "5.05ms", "memory": 0, "memory_str": null, "filename": "UpdateProductStockStatus.php:21", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FListeners%2FUpdateProductStockStatus.php&line=21", "ajax": false, "filename": "UpdateProductStockStatus.php", "line": "21"}, "connection": "adawliahshop", "explain": null, "start_percent": 23.479, "width_percent": 4.98}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 87 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 159}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 101}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.244793, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Product.php:159", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=159", "ajax": false, "filename": "Product.php", "line": "159"}, "connection": "adawliahshop", "explain": null, "start_percent": 28.459, "width_percent": 0.671}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 87 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 87, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "vendor/botble/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "vendor/botble/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.465434, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 29.129, "width_percent": 0.789}, {"sql": "select * from `slugs` where (`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product' and `reference_id` = 87) limit 1", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product", 87], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/botble/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.494498, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 29.918, "width_percent": 1.045}, {"sql": "delete from `audit_histories` where `created_at` < '2025-08-02 16:22:36' limit 1000", "type": "query", "params": [], "bindings": ["2025-08-02 16:22:36"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.574485, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 13, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "adawliahshop", "explain": null, "start_percent": 30.963, "width_percent": 1.597}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `user_type`, `actor_id`, `actor_type`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'product', 'updated', 1, 'Botble\\\\ACL\\\\Models\\\\User', 1, 'Botble\\\\ACL\\\\Models\\\\User', 87, 'Camera', 'primary', '2025-09-01 16:22:36', '2025-09-01 16:22:36', '{\\\"name\\\":\\\"Camera\\\",\\\"model\\\":\\\"Botble\\\\\\\\Ecommerce\\\\\\\\Models\\\\\\\\Product\\\",\\\"slug\\\":\\\"camera\\\",\\\"slug_id\\\":\\\"163\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"description\\\":\\\"<p>Camera<\\\\/p>\\\",\\\"content\\\":null,\\\"images\\\":[null],\\\"video_media\\\":\\\"[]\\\",\\\"product_type\\\":\\\"physical\\\",\\\"specification_table_id\\\":null,\\\"sale_type\\\":\\\"0\\\",\\\"sku\\\":\\\"SKU010\\\",\\\"price\\\":\\\"150\\\",\\\"sale_price\\\":null,\\\"start_date\\\":null,\\\"end_date\\\":null,\\\"cost_per_item\\\":null,\\\"product_id\\\":\\\"87\\\",\\\"barcode\\\":null,\\\"with_storehouse_management\\\":\\\"0\\\",\\\"quantity\\\":\\\"100\\\",\\\"allow_checkout_when_out_of_stock\\\":\\\"0\\\",\\\"stock_status\\\":\\\"in_stock\\\",\\\"weight\\\":null,\\\"length\\\":null,\\\"wide\\\":null,\\\"height\\\":null,\\\"is_added_attributes\\\":\\\"0\\\",\\\"has_product_options\\\":\\\"1\\\",\\\"related_products\\\":null,\\\"optional_products\\\":\\\"2,3\\\",\\\"faq_schema_config\\\":[[{\\\"key\\\":\\\"question\\\",\\\"value\\\":null},{\\\"key\\\":\\\"answer\\\",\\\"value\\\":null}]],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"status\\\":\\\"published\\\",\\\"store_id\\\":null,\\\"is_featured\\\":\\\"0\\\",\\\"categories\\\":[\\\"38\\\"],\\\"brand_id\\\":null,\\\"image\\\":\\\"https:\\\\/\\\\/images.pexels.com\\\\/photos\\\\/51383\\\\/photo-camera-subject-photographer-51383.jpeg\\\",\\\"minimum_order_quantity\\\":\\\"0\\\",\\\"maximum_order_quantity\\\":\\\"0\\\",\\\"tag\\\":null,\\\"submitter\\\":\\\"apply\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "product", "updated", 1, "Botble\\ACL\\Models\\User", 1, "Botble\\ACL\\Models\\User", 87, "Camera", "primary", "2025-09-01 16:22:36", "2025-09-01 16:22:36", "{\"name\":\"Camera\",\"model\":\"Botble\\\\Ecommerce\\\\Models\\\\Product\",\"slug\":\"camera\",\"slug_id\":\"163\",\"is_slug_editable\":\"1\",\"description\":\"<p>Camera<\\/p>\",\"content\":null,\"images\":[null],\"video_media\":\"[]\",\"product_type\":\"physical\",\"specification_table_id\":null,\"sale_type\":\"0\",\"sku\":\"SKU010\",\"price\":\"150\",\"sale_price\":null,\"start_date\":null,\"end_date\":null,\"cost_per_item\":null,\"product_id\":\"87\",\"barcode\":null,\"with_storehouse_management\":\"0\",\"quantity\":\"100\",\"allow_checkout_when_out_of_stock\":\"0\",\"stock_status\":\"in_stock\",\"weight\":null,\"length\":null,\"wide\":null,\"height\":null,\"is_added_attributes\":\"0\",\"has_product_options\":\"1\",\"related_products\":null,\"optional_products\":\"2,3\",\"faq_schema_config\":[[{\"key\":\"question\",\"value\":null},{\"key\":\"answer\",\"value\":null}]],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"status\":\"published\",\"store_id\":null,\"is_featured\":\"0\",\"categories\":[\"38\"],\"brand_id\":null,\"image\":\"https:\\/\\/images.pexels.com\\/photos\\/51383\\/photo-camera-subject-photographer-51383.jpeg\",\"minimum_order_quantity\":\"0\",\"maximum_order_quantity\":\"0\",\"tag\":null,\"submitter\":\"apply\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 69}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.580381, "duration": 0.01142, "duration_str": "11.42ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:69", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=69", "ajax": false, "filename": "AuditHandlerListener.php", "line": "69"}, "connection": "adawliahshop", "explain": null, "start_percent": 32.561, "width_percent": 11.261}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 87 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/AddLanguageForVariantsListener.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Listeners\\AddLanguageForVariantsListener.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5940259, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 43.822, "width_percent": 3.875}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'faq_schema_config' and `reference_id` = 87 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": ["faq_schema_config", 87, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/plugins/faq/src/FaqSupport.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\faq\\src\\FaqSupport.php", "line": 69}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/SaveProductFaqListener.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Listeners\\SaveProductFaqListener.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}], "start": **********.604243, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "adawliahshop", "explain": null, "start_percent": 47.697, "width_percent": 1.499}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'faq_ids' and `reference_id` = 87 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product') limit 1", "type": "query", "params": [], "bindings": ["faq_ids", 87, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/SaveProductFaqListener.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Listeners\\SaveProductFaqListener.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}], "start": **********.608802, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 49.196, "width_percent": 0.454}, {"sql": "select * from `ec_product_category_product` where `ec_product_category_product`.`product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 109}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.6162982, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:109", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=109", "ajax": false, "filename": "StoreProductService.php", "line": "109"}, "connection": "adawliahshop", "explain": null, "start_percent": 49.65, "width_percent": 1.144}, {"sql": "select * from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 111}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.62006, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:111", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=111", "ajax": false, "filename": "StoreProductService.php", "line": "111"}, "connection": "adawliahshop", "explain": null, "start_percent": 50.794, "width_percent": 1.597}, {"sql": "select * from `ec_product_label_products` where `ec_product_label_products`.`product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 113}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.623629, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:113", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=113", "ajax": false, "filename": "StoreProductService.php", "line": "113"}, "connection": "adawliahshop", "explain": null, "start_percent": 52.391, "width_percent": 1.647}, {"sql": "select * from `ec_tax_products` where `ec_tax_products`.`product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 115}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.627003, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:115", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=115", "ajax": false, "filename": "StoreProductService.php", "line": "115"}, "connection": "adawliahshop", "explain": null, "start_percent": 54.038, "width_percent": 1.104}, {"sql": "delete from `ec_product_related_relations` where `ec_product_related_relations`.`from_product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 118}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.629685, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:118", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=118", "ajax": false, "filename": "StoreProductService.php", "line": "118"}, "connection": "adawliahshop", "explain": null, "start_percent": 55.142, "width_percent": 1.183}, {"sql": "delete from `ec_product_optional_relations` where `ec_product_optional_relations`.`from_product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 126}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.6334229, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:126", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=126", "ajax": false, "filename": "StoreProductService.php", "line": "126"}, "connection": "adawliahshop", "explain": null, "start_percent": 56.326, "width_percent": 1.075}, {"sql": "insert into `ec_product_optional_relations` (`from_product_id`, `to_product_id`) values (87, '2'), (87, '3')", "type": "query", "params": [], "bindings": [87, "2", 87, "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 129}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.6361911, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:129", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=129", "ajax": false, "filename": "StoreProductService.php", "line": "129"}, "connection": "adawliahshop", "explain": null, "start_percent": 57.401, "width_percent": 3.974}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 172}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.6419098, "duration": 0.00618, "duration_str": "6.18ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:172", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=172", "ajax": false, "filename": "StoreProductService.php", "line": "172"}, "connection": "adawliahshop", "explain": null, "start_percent": 61.375, "width_percent": 6.094}, {"sql": "select * from `ec_options` where `ec_options`.`product_id` = 87 and `ec_options`.`product_id` is not null and 1 = 1 order by `order` asc", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 341}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 180}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.649641, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 67.469, "width_percent": 3.787}, {"sql": "select * from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 190}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.657026, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:190", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=190", "ajax": false, "filename": "StoreProductService.php", "line": "190"}, "connection": "adawliahshop", "explain": null, "start_percent": 71.255, "width_percent": 1.874}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 87 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6604002, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "UpdateProductStockStatus.php:21", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FListeners%2FUpdateProductStockStatus.php&line=21", "ajax": false, "filename": "UpdateProductStockStatus.php", "line": "21"}, "connection": "adawliahshop", "explain": null, "start_percent": 73.129, "width_percent": 0.316}, {"sql": "select `ec_product_tags`.*, `ec_product_tag_product`.`product_id` as `pivot_product_id`, `ec_product_tag_product`.`tag_id` as `pivot_tag_id` from `ec_product_tags` inner join `ec_product_tag_product` on `ec_product_tags`.`id` = `ec_product_tag_product`.`tag_id` where `ec_product_tag_product`.`product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductTagService.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductTagService.php", "line": 18}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.662363, "duration": 0.00862, "duration_str": "8.62ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "adawliahshop", "explain": null, "start_percent": 73.444, "width_percent": 8.5}, {"sql": "select count(*) as aggregate from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 87 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 203}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.6738951, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:203", "source": {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 203}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=203", "ajax": false, "filename": "ProductController.php", "line": "203"}, "connection": "adawliahshop", "explain": null, "start_percent": 81.945, "width_percent": 0.473}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = 87", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 204}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.675967, "duration": 0.016120000000000002, "duration_str": "16.12ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:204", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=204", "ajax": false, "filename": "ProductController.php", "line": "204"}, "connection": "adawliahshop", "explain": null, "start_percent": 82.418, "width_percent": 15.896}, {"sql": "select `product_id` from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 87 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 219}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.6937318, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:219", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 219}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=219", "ajax": false, "filename": "ProductController.php", "line": "219"}, "connection": "adawliahshop", "explain": null, "start_percent": 98.314, "width_percent": 0.937}, {"sql": "update `ec_products` set `status` = 'published', `ec_products`.`updated_at` = '2025-09-01 16:22:36' where 0 = 1", "type": "query", "params": [], "bindings": [{"value": "published", "label": "Published"}, "2025-09-01 16:22:36"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 221}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.696923, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:221", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 221}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=221", "ajax": false, "filename": "ProductController.php", "line": "221"}, "connection": "adawliahshop", "explain": null, "start_percent": 99.251, "width_percent": 0.749}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Base\\Models\\MetaBox": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}}, "count": 7, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 6, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://adawliahshop.gc/admin/ecommerce/products/edit/87", "action_name": "products.update", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@update", "uri": "POST admin/ecommerce/products/edit/{product}", "controller": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=153\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=153\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/ProductController.php:153-227</a>", "middleware": "web, core, auth", "duration": "1.53s", "peak_memory": "56MB", "response": "Redirect to https://adawliahshop.gc/admin/ecommerce/products/edit/87", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-834814408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-834814408\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-93428952 data-indent-pad=\"  \"><span class=sf-dump-note>array:46</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Camera</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Botble\\Ecommerce\\Models\\Product</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"6 characters\">camera</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">163</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&lt;p&gt;Camera&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>video_media</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n  \"<span class=sf-dump-key>product_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">physical</span>\"\n  \"<span class=sf-dump-key>specification_table_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sale_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"6 characters\">SKU010</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cost_per_item</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">87</span>\"\n  \"<span class=sf-dump-key>barcode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>with_storehouse_management</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n  \"<span class=sf-dump-key>allow_checkout_when_out_of_stock</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">in_stock</span>\"\n  \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>length</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wide</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>height</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_added_attributes</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>has_product_options</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>related_products</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>optional_products</span>\" => \"<span class=sf-dump-str title=\"3 characters\">2,3</span>\"\n  \"<span class=sf-dump-key>faq_schema_config</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>key</span>\" => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>key</span>\" => \"<span class=sf-dump-str title=\"6 characters\">answer</span>\"\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">38</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"83 characters\">https://images.pexels.com/photos/51383/photo-camera-subject-photographer-51383.jpeg</span>\"\n  \"<span class=sf-dump-key>minimum_order_quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>maximum_order_quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>tag</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-93428952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-232433047 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">adawliahshop.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5599</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://adawliahshop.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary6wvigtBtdfvFqIrM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">https://adawliahshop.gc/admin/ecommerce/products/edit/87</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2662 characters\">botble_footprints_cookie=eyJpdiI6InBYMHJNeEtkbHFlbWpBZU5QVnQrYlE9PSIsInZhbHVlIjoiM0JxSk5CTXlleEZZMzNCb3h5cUdTVXBBajFKTVI0VFZxemc3eWtvNWdoZHF2bUdRbkZUM2hscVkvcWk1Q2RJRVRTVDBGNmZjVEpZNFozWFU4K0RsL09Fby9BZGx3NGRHdjdwbDFZWDg4YmJZUkpmSmJXa0EwSlRJUWhmTEdqY3UiLCJtYWMiOiI3ZDRiNDUwNGFkYzNlOWZhYTNkNTNkOTlkYTMwYTkxY2U0MTAxMzJlZDFlMzNjOGJjZTZjYWFlMGI2ZGRmMzNiIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IjBNcGkyazd4dExDbnlaSmNmUnpOd0E9PSIsInZhbHVlIjoiMUtFOGhLckFUdnpGWVRyekl0MVpCVTZSKytUYitPNk5LR3oxTUdTbDdHZUt5MmtWS1ZJV2d0NFNNY1FDeWhnenJ4VDhRQk9BMDRqNklkZXU5UlNHZ1dBdStUUThWZHl2dkJzUDh2V09HMDRwa0NCY0NsTmt3NGV4MVlBWmhmRDUvbnpKOGZXcnU4aHJMWjZqVnB3akdLWGxsWmlHNVpSSVFhUFIvbTM2UE5IY01IVDhUMTlLN3lST1RoWW1iYVFQeTdpdjBsYjZrUTl2eE5oSnJLMU8rZUFqRjVDaVZUYTRBamxBM3phMUd2SlZTOGVyNXh0NzNxeVdXdmU0N0pLZGU0OVJvSUU4YXg4ZmM3dDVsMkRwSkx4RjN1NWN4aW1QbS9QSGJLdjlUdktvRW1UR1BIT3hqYUZSRy84YnZGQmxRT3VpZ2JsSGlGZDM2MmF3KzdzMms2VEJ0UVhZSE0vTzZKWU5pQ3JKYUhhbXhKc0dxK056dWZHTkp3QlZ6bW1LRFpxcmlkeGpXMHpoNG5uS0hkSitQWlhPTnJWb1Z4TDZXcDJFNmNFVTZjcW5DNmMwQUd3QWhoUVMySGpIZUkrRWxlamNVOC9KYzl1ZlN6aWUrSTdHWlZGYVorZGkwVFo4Nys1UG5IcDdFcjBYSmE3anRRQXhGcWdZNzlQQWpCVTFpZDh3RHJMaG1EZHRkVXpoUFVUNlhnPT0iLCJtYWMiOiIxZTdkY2JiZTI5NWNmMjdkZGQ5YThhYjdlYmUwMzk4NWZjZDkzYzJiNmYwZDFhMmE5ZDA3NDdiZmQzY2E0MjBhIiwidGFnIjoiIn0%3D; perf_dv6Tr4n=1; botble_cookie_newsletter=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkpIaFpaMEp5SFBNQ0pmZisvZStlaFE9PSIsInZhbHVlIjoiVXJHbWNMUmptbk5paVZvK0ZHUHNDaDFVQWp3L1NOMEJVdWJnWVFKWHhHWWJ5ZlF5TkpZRE1sWGRBRk85bUtUek9xVUc0cHV3cEkzYUVLbzgzQTVvUUJwV2NBaGQ0R3ROUG9xN3hodUxicG9XUS9OZlE1dVVIRXBQaDg4REF4NEpHWjhrZ0Z4Mk9iV293d1JDd0p0YXR4ejNDZTdCNEc3SGRSUWpUQ3JLUmtlcUx5S250TWI3ajY5SkFqRFgvOFMxY3RRVjFQdEtKR3ppVGRnaVg2dVpKUXdWZ0NzVndkdDVqZU9la2FGdmFDcz0iLCJtYWMiOiJmMjllOGFmY2RjOGE0OTQ2ZjdjMjdlMDVmMjFhZWY5ZDA2NTcxNTkzNDA4NmU1OGNlZWNlZjJkM2VkOGE4M2U0IiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; XSRF-TOKEN=eyJpdiI6IkpEc1NXSjFScFBHM2VVdGo1R3pleEE9PSIsInZhbHVlIjoiOWN6ckp5SThVQ3dYeXRzMy9VUXp2aU10R3BvQm12Q3gzbGJYNHpRSGU5YWtJZEJ6T0RYcVdNVURkZ3Vmd3BOVndvODRpNFBYbTFiNTZ6U2hXRUNST25Nd3ZWWEorbFV5SW5uNm5OQnRtOVc0TWsxOEg3K0NSTHJOaWVKalVpZGkiLCJtYWMiOiJiZWE1ZDgyNzYxZDU1YjcxYTMzM2IxNDcwY2JmNDZmMDAzMWRlZWY2OTNhMWQzNzY2NjBjNmY4NTkwNzgwZGYxIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6ImJHMWVUTS96MGRSMVFHQWx1WCtOK1E9PSIsInZhbHVlIjoiL3ZzODVNNVpxZVFIU0dLU01rQnpidVdIdnVQVkpQTGhjTTJ1cnFBTUhuYXpOaGgxM21pV3R1NGU4V1hSTFhzcXRERElVZWx0KzUrb3psclBnNWpGWFp6aXZxeTVkQ2xYcWpJOG1pVklGbVA1bFNsa1RLS3I0VlNZMk5BWWJqVGMiLCJtYWMiOiJmMzM3MDViMzM4MTY0MWYxMzI1YzY3OThhZjdiYzcxZmExNWI5ZmI2YWVmMzBhNzUzMWFjMmJiMTQ0ZGQwMjc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232433047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b611e49ca9c9e01d12f55b9fdbec097ba769ddeb</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;footprint&quot;:&quot;b611e49ca9c9e01d12f55b9fdbec097ba769ddeb&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;adawliahshop.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOi7kX3uphlBCTLAiO3znuue74DN3GeKZVAdyGvz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-529443965 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 01 Sep 2025 16:22:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">https://adawliahshop.gc/admin/ecommerce/products/edit/87</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529443965\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1215672129 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>5</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>+</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://adawliahshop.gc/admin/ecommerce/products/edit/87</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215672129\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://adawliahshop.gc/admin/ecommerce/products/edit/87", "action_name": "products.update", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@update"}, "badge": "302 Found"}}