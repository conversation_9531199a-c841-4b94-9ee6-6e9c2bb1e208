/**
 * Branch Management - Product Page JavaScript
 */

class BranchProductManager {
    constructor() {
        this.citySelect = document.getElementById('product-city-select');
        this.branchesContainer = document.getElementById('product-branches-list');
        this.ajaxUrl = document.querySelector('.product-branches-section')?.dataset.ajaxUrl;
        this.messages = this.getMessages();

        this.init();
    }

    init() {
        if (this.citySelect) {
            this.citySelect.addEventListener('change', (e) => this.handleCityChange(e));
        }
    }

    getMessages() {
        const dataElement = document.getElementById('branch-management-data');
        if (dataElement) {
            try {
                return JSON.parse(dataElement.textContent).messages;
            } catch (e) {
                console.error('Error parsing branch management data:', e);
            }
        }

        // Fallback messages
        return {
            selectCity: 'Please select a city to view available branches.',
            loading: 'Loading branches...',
            selectBranch: 'Select Branch for Pickup',
            chooseBranch: 'Choose a branch...',
            pickupAvailable: 'Pickup Available',
            pickupNotAvailable: 'Pickup Not Available',
            address: 'Address',
            phone: 'Phone',
            noBranches: 'No branches available in this city.',
            error: 'Error loading branches. Please try again.'
        };
    }

    handleCityChange(event) {
        const cityId = event.target.value;

        if (!cityId) {
            this.showDefaultMessage();
            return;
        }

        this.showLoading();
        this.loadBranches(cityId);
    }

    showDefaultMessage() {
        this.branchesContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> ${this.messages.selectCity}
            </div>
        `;
    }

    showLoading() {
        this.branchesContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fa fa-spinner fa-spin"></i> ${this.messages.loading}
            </div>
        `;
    }

    async loadBranches(cityId) {
        try {
            // Get product ID if we're on a product page
            const productId = this.getProductId();
            let url = `${this.ajaxUrl}?city_id=${cityId}`;

            if (productId) {
                url += `&product_id=${productId}`;
            }

            const response = await fetch(url);
            const branches = await response.json();

            this.renderBranches(branches);
        } catch (error) {
            console.error('Error loading branches:', error);
            this.showError();
        }
    }

    getProductId() {
        // Try to get product ID from various sources
        const productIdInput = document.querySelector('input[name="id"]');
        if (productIdInput) {
            return productIdInput.value;
        }

        // Try to get from URL or other sources
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('product_id') || null;
    }

    renderBranches(branches) {
        if (branches.length === 0) {
            this.showNoBranches();
            return;
        }

        const cityName = this.citySelect.options[this.citySelect.selectedIndex].text;
        let html = this.createBranchesHeader(cityName, branches.length);
        html += this.createBranchSelector(branches);
        html += this.createBranchCards(branches);

        this.branchesContainer.innerHTML = html;
        this.attachBranchSelectListener(branches);
    }

    createBranchesHeader(cityName, count) {
        return `<h6><i class="fa fa-store"></i> ${cityName} Branches (${count})</h6>`;
    }

    createBranchSelector(branches) {
        let html = `
            <div class="branch-selector mb-3">
                <label for="selected-branch" class="form-label">${this.messages.selectBranch}</label>
                <select id="selected-branch" class="form-control">
                    <option value="">${this.messages.chooseBranch}</option>
        `;

        branches.forEach(branch => {
            const statusText = branch.is_pickup_available ? '' : ' (Pickup Not Available)';
            const disabled = !branch.is_pickup_available ? 'disabled' : '';

            html += `<option value="${branch.id}" ${disabled}>${branch.name} - ${statusText}</option>`;
        });

        html += `
                </select>
            </div>
            <div id="selected-branch-details" style="display: none;">
                <!-- Branch details will show here -->
            </div>
        `;

        return html;
    }

    createBranchCards(branches) {
        let html = '<div class="branches-cards mt-3">';

        branches.forEach(branch => {
            const pickupBadge = branch.is_pickup_available ?
                `<span class="badge bg-success mb-2">${this.messages.pickupAvailable}</span>` :
                `<span class="badge bg-secondary mb-2">${this.messages.pickupNotAvailable}</span>`;

            const opacityClass = !branch.is_pickup_available ? 'opacity-75' : '';
            const phoneHtml = branch.phone ? `<p class="branch-phone mb-1 text-muted"><i class="fa fa-phone"></i> ${branch.phone}</p>` : '';

            // Add stock information if available
            let stockHtml = '';
            if (branch.stock) {
                const stockClass = branch.stock.is_out_of_stock ? 'text-danger' :
                                 branch.stock.is_low_stock ? 'text-warning' : 'text-success';
                const stockText = branch.stock.is_out_of_stock ? 'Out of Stock' :
                                branch.stock.is_low_stock ? `Low Stock (${branch.stock.quantity})` :
                                `In Stock (${branch.stock.quantity})`;

                stockHtml = `<p class="branch-stock mb-1 ${stockClass}">
                    <i class="fa fa-box"></i> ${stockText}
                </p>`;
            }

            html += `
                <div class="branch-item border rounded p-3 mb-2 ${opacityClass}">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="branch-name mb-1">${branch.name}</h6>
                            <p class="branch-address mb-1 text-muted">
                                <i class="fa fa-map-marker-alt"></i> ${branch.full_address}
                            </p>
                            ${phoneHtml}
                            ${stockHtml}
                        </div>
                        <div class="col-md-4 text-end">
                            ${pickupBadge}
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    attachBranchSelectListener(branches) {
        const branchSelect = document.getElementById('selected-branch');
        if (!branchSelect) return;

        branchSelect.addEventListener('change', (event) => {
            const branchId = event.target.value;
            const detailsDiv = document.getElementById('selected-branch-details');

            if (branchId) {
                const selectedBranch = branches.find(b => b.id == branchId);
                if (selectedBranch) {
                    this.showBranchDetails(selectedBranch, detailsDiv);
                }
            } else {
                detailsDiv.style.display = 'none';
            }
        });
    }

    showBranchDetails(branch, detailsDiv) {
        const phoneHtml = branch.phone ? `<p><strong>${this.messages.phone}:</strong><br>${branch.phone}</p>` : '';

        const detailsHtml = `
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title"><i class="fa fa-store"></i> ${branch.name}</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>${this.messages.address}:</strong><br>${branch.full_address}</p>
                            ${phoneHtml}
                        </div>
                        <div class="col-md-6">
                        </div>
                    </div>
                </div>
            </div>
        `;

        detailsDiv.innerHTML = detailsHtml;
        detailsDiv.style.display = 'block';
    }

    showNoBranches() {
        this.branchesContainer.innerHTML = `
            <div class="alert alert-warning">
                <i class="fa fa-exclamation-triangle"></i> ${this.messages.noBranches}
            </div>
        `;
    }

    showError() {
        this.branchesContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-circle"></i> ${this.messages.error}
            </div>
        `;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('product-city-select')) {
        new BranchProductManager();
    }
});
