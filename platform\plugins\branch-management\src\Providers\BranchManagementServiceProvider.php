<?php

namespace Bo<PERSON>ble\BranchManagement\Providers;

use Illuminate\Support\Arr;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Base\Facades\DashboardMenu;
use Botble\Base\Facades\MacroableModels;
use Botble\Base\Supports\ServiceProvider;
use <PERSON><PERSON>ble\Ecommerce\Models\OrderAddress;
use Bo<PERSON>ble\BranchManagement\Models\Branch;
use Illuminate\Routing\Events\RouteMatched;
use Botble\Base\Facades\PanelSectionManager;
use Botble\Base\PanelSections\PanelSectionItem;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Bo<PERSON>ble\BranchManagement\Providers\EventServiceProvider;
use Bo<PERSON>ble\Ecommerce\PanelSections\SettingEcommercePanelSection;
use Botble\BranchManagement\Repositories\Eloquent\BranchRepository;
use Botble\BranchManagement\Repositories\Interfaces\BranchInterface;

class BranchManagementServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(BranchInterface::class, function () {
            return new BranchRepository(new Branch());
        });
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/branch-management')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'email'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes(['web', 'api']);

        $this->app['events']->listen(RouteMatched::class, function (): void {
            DashboardMenu::registerItem([
                'id' => 'cms-plugins-branch-management',
                'priority' => 5,
                'parent_id' => null,
                'name' => 'plugins/branch-management::branch.name',
                'icon' => 'ti ti-building-store',
                'url' => route('branches.index'),
                'permissions' => ['branches.index'],
            ]);
        });

        $this->app->booted(function (): void {
            $this->registerShortcodes();


            // Add extra fillable columns
            MacroableModels::addMacro(OrderAddress::class, 'extraFillable', function () {
                return ['branch_id', 'is_branch_pickup'];
            });

            // Add extra casts
            MacroableModels::addMacro(OrderAddress::class, 'extraCasts', function () {
                return [
                    'branch_id' => 'integer',
                    'is_branch_pickup' => 'boolean',
                ];
            });

            // Inject branch relation
            OrderAddress::resolveRelationUsing('branch', function ($model) {
                return $model->belongsTo(Branch::class, 'branch_id')->withDefault();
            });

            $this->app->register(HookServiceProvider::class);
            $this->app->register(EventServiceProvider::class);
        });

        // Register settings in Ecommerce panel section
        PanelSectionManager::beforeRendering(function (): void {
            if (is_plugin_active('ecommerce')) {
                PanelSectionManager::default()->registerItem(
                    SettingEcommercePanelSection::class,
                    fn() => PanelSectionItem::make('settings.ecommerce.branch_management')
                        ->setTitle(trans('plugins/branch-management::branch.name'))
                        ->withIcon('ti ti-building-store')
                        ->withDescription(trans('plugins/branch-management::settings.description'))
                        ->withPriority(160)
                        ->withRoute('branch-management.settings.edit')
                );
            }
        });
    }

    protected function registerShortcodes(): void
    {

        add_shortcode('branches-list', __('Branches List'), __('Display all branches grouped by city'), function ($shortcode) {
            return view('plugins/branch-management::shortcodes.branches-list', [
                'city_id' => $shortcode->city_id ?? null,
                'limit' => $shortcode->limit ?? 10,
            ])->render();
        });
    }
}
