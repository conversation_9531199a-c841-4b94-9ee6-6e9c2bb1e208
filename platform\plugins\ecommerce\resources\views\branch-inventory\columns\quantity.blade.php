<div class="d-flex align-items-center">
    <input 
        type="number" 
        class="form-control form-control-sm quantity-input" 
        value="{{ $item->quantity }}" 
        min="0"
        data-branch-id="{{ $item->branch_id }}"
        data-product-id="{{ $item->product_id }}"
        style="width: 80px; margin-right: 10px;"
    >
    @if($item->isOutOfStock())
        <span class="badge badge-danger">{{ $item->quantity }}</span>
    @elseif($item->isLowStock())
        <span class="badge badge-warning">{{ $item->quantity }}</span>
    @else
        <span class="badge badge-success">{{ $item->quantity }}</span>
    @endif
</div>
