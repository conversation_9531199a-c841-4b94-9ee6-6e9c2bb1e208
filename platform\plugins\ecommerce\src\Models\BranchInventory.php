<?php

namespace Bo<PERSON>ble\Ecommerce\Models;

use Botble\Base\Models\BaseModel;
use Botble\BranchManagement\Models\Branch;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class BranchInventory extends BaseModel
{
    protected $table = 'ec_branch_inventory';

    protected $fillable = [
        'branch_id',
        'product_id',
        'quantity',
        'min_stock_alert',
    ];

    protected $casts = [
        'quantity' => 'int',
        'min_stock_alert' => 'int',
    ];

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Check if the product is in stock at this branch
     */
    public function isInStock(): bool
    {
        return $this->quantity > 0;
    }

    /**
     * Check if the product is low stock at this branch
     */
    public function isLowStock(): bool
    {
        return $this->quantity > 0 && $this->quantity <= $this->min_stock_alert;
    }

    /**
     * Check if the product is out of stock at this branch
     */
    public function isOutOfStock(): bool
    {
        return $this->quantity <= 0;
    }

    /**
     * Check if sufficient quantity is available
     */
    public function hasSufficientStock(int $requiredQuantity): bool
    {
        return $this->quantity >= $requiredQuantity;
    }

    /**
     * Scope to filter by branch
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope to filter by product
     */
    public function scopeByProduct(Builder $query, int $productId): Builder
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope to filter in stock items
     */
    public function scopeInStock(Builder $query): Builder
    {
        return $query->where('quantity', '>', 0);
    }

    /**
     * Scope to filter out of stock items
     */
    public function scopeOutOfStock(Builder $query): Builder
    {
        return $query->where('quantity', '<=', 0);
    }

    /**
     * Scope to filter low stock items
     */
    public function scopeLowStock(Builder $query): Builder
    {
        return $query->whereRaw('quantity > 0 AND quantity <= min_stock_alert');
    }
}
