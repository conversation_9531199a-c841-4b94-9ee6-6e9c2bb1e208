<?php

namespace Bo<PERSON>ble\Ecommerce\Services;

use <PERSON><PERSON>ble\Ecommerce\Models\Order;
use <PERSON><PERSON>ble\Ecommerce\Models\OrderProduct;
use <PERSON><PERSON>ble\Ecommerce\Models\Product;
use Botble\Ecommerce\Events\ProductQuantityUpdatedEvent;
use Illuminate\Support\Facades\DB;
use Exception;

class BranchOrderService
{
    public function __construct(protected BranchInventoryService $branchInventoryService)
    {
    }

    /**
     * Get the branch inventory service
     */
    public function getBranchInventoryService(): BranchInventoryService
    {
        return $this->branchInventoryService;
    }

    /**
     * Process order with branch-specific inventory deduction
     */
    public function processOrderWithBranchInventory(Order $order, array $products, ?int $branchId = null): bool
    {
        if (!$branchId) {
            // If no branch specified, use the traditional global inventory system
            return $this->processOrderWithGlobalInventory($order, $products);
        }

        return DB::transaction(function () use ($order, $products, $branchId) {
            foreach ($products as $productItem) {
                $productId = $productItem['id'] ?? null;
                $quantity = $productItem['quantity'] ?? 1;

                if (!$productId) {
                    continue;
                }

                $product = Product::find($productId);
                if (!$product) {
                    continue;
                }

                // Check if product has storehouse management enabled
                if (!$product->with_storehouse_management) {
                    continue;
                }

                // Check branch inventory availability
                if (!$product->hasSufficientStockAtBranch($branchId, $quantity)) {
                    throw new Exception("Insufficient stock for product '{$product->name}' at selected branch");
                }

                // Deduct stock from branch inventory
                $this->branchInventoryService->reduceStock($branchId, $productId, $quantity);

                // Update the order product with branch information
                $this->updateOrderProductWithBranch($order->id, $productId, $branchId);

                // Trigger event for parent product updates if needed
                event(new ProductQuantityUpdatedEvent($product));
            }

            return true;
        });
    }

    /**
     * Process order with traditional global inventory
     */
    protected function processOrderWithGlobalInventory(Order $order, array $products): bool
    {
        foreach ($products as $productItem) {
            $productId = $productItem['id'] ?? null;
            $quantity = $productItem['quantity'] ?? 1;

            if (!$productId) {
                continue;
            }

            $product = Product::find($productId);
            if (!$product) {
                continue;
            }

            // Only decrease quantity if storehouse management is enabled and sufficient stock
            if ($product->with_storehouse_management && $product->quantity >= $quantity) {
                $product->quantity -= $quantity;
                $product->save();

                // Trigger event to update parent product if this is a variation
                event(new ProductQuantityUpdatedEvent($product));
            }
        }

        return true;
    }

    /**
     * Update order product with branch information
     */
    protected function updateOrderProductWithBranch(int $orderId, int $productId, int $branchId): void
    {
        OrderProduct::where('order_id', $orderId)
            ->where('product_id', $productId)
            ->update(['branch_id' => $branchId]);
    }

    /**
     * Restore stock when order is cancelled
     */
    public function restoreStockOnOrderCancellation(Order $order): bool
    {
        return DB::transaction(function () use ($order) {
            foreach ($order->products as $orderProduct) {
                $product = Product::find($orderProduct->product_id);
                if (!$product || !$product->with_storehouse_management) {
                    continue;
                }

                if ($orderProduct->branch_id) {
                    // Restore to branch inventory
                    $this->branchInventoryService->addStock(
                        $orderProduct->branch_id,
                        $orderProduct->product_id,
                        $orderProduct->qty
                    );
                } else {
                    // Restore to global inventory
                    $product->quantity += $orderProduct->qty;
                    $product->save();
                }

                // Trigger event for parent product updates
                event(new ProductQuantityUpdatedEvent($product));
            }

            return true;
        });
    }

    /**
     * Validate stock availability before order creation
     */
    public function validateStockAvailability(array $products, ?int $branchId = null): array
    {
        $errors = [];

        foreach ($products as $productItem) {
            $productId = $productItem['id'] ?? null;
            $quantity = $productItem['quantity'] ?? 1;

            if (!$productId) {
                continue;
            }

            $product = Product::find($productId);
            if (!$product) {
                $errors[] = "Product with ID {$productId} not found";
                continue;
            }

            if (!$product->with_storehouse_management) {
                continue;
            }

            if ($branchId) {
                // Check branch-specific stock
                if (!$product->hasSufficientStockAtBranch($branchId, $quantity)) {
                    $availableStock = $product->getStockAtBranch($branchId);
                    $errors[] = "Insufficient stock for '{$product->name}' at selected branch. Available: {$availableStock}, Required: {$quantity}";
                }
            } else {
                // Check global stock
                if ($product->quantity < $quantity) {
                    $errors[] = "Insufficient stock for '{$product->name}'. Available: {$product->quantity}, Required: {$quantity}";
                }
            }
        }

        return $errors;
    }

    /**
     * Get branch ID from order address or shipping information
     */
    public function getBranchIdFromOrder(Order $order): ?int
    {
        // Check if order has branch pickup
        if ($order->shippingAddress && $order->shippingAddress->branch_id) {
            return $order->shippingAddress->branch_id;
        }

        // Check if shipping method indicates branch pickup
        if ($order->shipping_method === 'branch_pickup') {
            // Extract branch ID from shipping option or other sources
            return $this->extractBranchIdFromShippingOption($order->shipping_option);
        }

        return null;
    }

    /**
     * Extract branch ID from shipping option
     */
    protected function extractBranchIdFromShippingOption(?string $shippingOption): ?int
    {
        if (!$shippingOption) {
            return null;
        }

        // Parse shipping option to extract branch ID
        // This depends on how the shipping option is formatted
        if (preg_match('/branch_(\d+)/', $shippingOption, $matches)) {
            return (int) $matches[1];
        }

        return null;
    }
}
