{"__meta": {"id": "01K43422PKFSV0KFCBR9Y0SE21", "datetime": "2025-09-01 17:09:09", "utime": **********.972354, "method": "GET", "uri": "/admin/ecommerce/branch-inventory", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 388, "start": 1756746548.287307, "end": **********.972368, "duration": 1.685060977935791, "duration_str": "1.69s", "measures": [{"label": "Booting", "start": 1756746548.287307, "relative_start": 0, "end": **********.210796, "relative_end": **********.210796, "duration": 0.****************, "duration_str": "923ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.210815, "relative_start": 0.****************, "end": **********.97237, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "762ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.228869, "relative_start": 0.****************, "end": **********.238184, "relative_end": **********.238184, "duration": 0.009315013885498047, "duration_str": "9.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::bulk-changes", "start": **********.296733, "relative_start": 1.****************, "end": **********.296733, "relative_end": **********.296733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.299576, "relative_start": 1.****************, "end": **********.299576, "relative_end": **********.299576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53dbbce7846c20f5734d935076bf04ca", "start": **********.302021, "relative_start": 1.014714002609253, "end": **********.302021, "relative_end": **********.302021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.303616, "relative_start": 1.0163090229034424, "end": **********.303616, "relative_end": **********.303616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.304125, "relative_start": 1.0168180465698242, "end": **********.304125, "relative_end": **********.304125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.306126, "relative_start": 1.0188190937042236, "end": **********.306126, "relative_end": **********.306126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.306949, "relative_start": 1.0196418762207031, "end": **********.306949, "relative_end": **********.306949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::badge", "start": **********.307518, "relative_start": 1.0202109813690186, "end": **********.307518, "relative_end": **********.307518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.310094, "relative_start": 1.022787094116211, "end": **********.969646, "relative_end": **********.969646, "duration": 0.6595518589019775, "duration_str": "660ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::table", "start": **********.310662, "relative_start": 1.0233550071716309, "end": **********.310662, "relative_end": **********.310662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::base-table", "start": **********.310941, "relative_start": 1.0236339569091797, "end": **********.310941, "relative_end": **********.310941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.313353, "relative_start": 1.0260460376739502, "end": **********.313353, "relative_end": **********.313353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::14ad31fb3af14d3ba24d3c578af35e73", "start": **********.314715, "relative_start": 1.0274078845977783, "end": **********.314715, "relative_end": **********.314715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::filter", "start": **********.31688, "relative_start": 1.0295729637145996, "end": **********.31688, "relative_end": **********.31688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.318361, "relative_start": 1.0310540199279785, "end": **********.318361, "relative_end": **********.318361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.319931, "relative_start": 1.0326240062713623, "end": **********.319931, "relative_end": **********.319931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.320363, "relative_start": 1.0330560207366943, "end": **********.320363, "relative_end": **********.320363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.321004, "relative_start": 1.0336968898773193, "end": **********.321004, "relative_end": **********.321004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.32134, "relative_start": 1.0340330600738525, "end": **********.32134, "relative_end": **********.32134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7b9d721822c50540453b6fa0f4bd081", "start": **********.322305, "relative_start": 1.0349979400634766, "end": **********.322305, "relative_end": **********.322305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.322884, "relative_start": 1.0355770587921143, "end": **********.322884, "relative_end": **********.322884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.323543, "relative_start": 1.036236047744751, "end": **********.323543, "relative_end": **********.323543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.select", "start": **********.323889, "relative_start": 1.0365819931030273, "end": **********.323889, "relative_end": **********.323889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.324661, "relative_start": 1.0373539924621582, "end": **********.324661, "relative_end": **********.324661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.32501, "relative_start": 1.037703037261963, "end": **********.32501, "relative_end": **********.32501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.325564, "relative_start": 1.0382568836212158, "end": **********.325564, "relative_end": **********.325564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.326304, "relative_start": 1.038996934890747, "end": **********.326304, "relative_end": **********.326304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c8728926b3975e33a051ebb6ef68e5d", "start": **********.327267, "relative_start": 1.0399599075317383, "end": **********.327267, "relative_end": **********.327267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.index", "start": **********.32785, "relative_start": 1.0405430793762207, "end": **********.32785, "relative_end": **********.32785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.body.index", "start": **********.328184, "relative_start": 1.040876865386963, "end": **********.328184, "relative_end": **********.328184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.328471, "relative_start": 1.0411639213562012, "end": **********.328471, "relative_end": **********.328471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::bulk-action", "start": **********.330288, "relative_start": 1.0429809093475342, "end": **********.330288, "relative_end": **********.330288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.331826, "relative_start": 1.0445189476013184, "end": **********.331826, "relative_end": **********.331826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.332437, "relative_start": 1.0451300144195557, "end": **********.332437, "relative_end": **********.332437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.333041, "relative_start": 1.04573392868042, "end": **********.333041, "relative_end": **********.333041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.334314, "relative_start": 1.0470070838928223, "end": **********.334314, "relative_end": **********.334314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.335393, "relative_start": 1.0480859279632568, "end": **********.335393, "relative_end": **********.335393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42e1966f95bce065f65d4b22e53f3772", "start": **********.336219, "relative_start": 1.0489120483398438, "end": **********.336219, "relative_end": **********.336219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.337712, "relative_start": 1.0504050254821777, "end": **********.337712, "relative_end": **********.337712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c8728926b3975e33a051ebb6ef68e5d", "start": **********.338337, "relative_start": 1.051029920578003, "end": **********.338337, "relative_end": **********.338337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.33859, "relative_start": 1.0512828826904297, "end": **********.33859, "relative_end": **********.33859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.3398, "relative_start": 1.0524928569793701, "end": **********.3398, "relative_end": **********.3398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::modal", "start": **********.340625, "relative_start": 1.0533180236816406, "end": **********.340625, "relative_end": **********.340625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.341501, "relative_start": 1.0541939735412598, "end": **********.341501, "relative_end": **********.341501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.342232, "relative_start": 1.0549249649047852, "end": **********.342232, "relative_end": **********.342232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.342907, "relative_start": 1.0555999279022217, "end": **********.342907, "relative_end": **********.342907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.343609, "relative_start": 1.0563020706176758, "end": **********.343609, "relative_end": **********.343609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.344186, "relative_start": 1.0568790435791016, "end": **********.344186, "relative_end": **********.344186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.344952, "relative_start": 1.0576450824737549, "end": **********.344952, "relative_end": **********.344952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.345512, "relative_start": 1.0582048892974854, "end": **********.345512, "relative_end": **********.345512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.346093, "relative_start": 1.0587859153747559, "end": **********.346093, "relative_end": **********.346093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.346384, "relative_start": 1.0590770244598389, "end": **********.346384, "relative_end": **********.346384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.346619, "relative_start": 1.059311866760254, "end": **********.346619, "relative_end": **********.346619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.347675, "relative_start": 1.0603680610656738, "end": **********.347675, "relative_end": **********.347675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.348512, "relative_start": 1.0612049102783203, "end": **********.348512, "relative_end": **********.348512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.349088, "relative_start": 1.0617809295654297, "end": **********.349088, "relative_end": **********.349088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.349861, "relative_start": 1.062553882598877, "end": **********.349861, "relative_end": **********.349861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.350191, "relative_start": 1.0628840923309326, "end": **********.350191, "relative_end": **********.350191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.351112, "relative_start": 1.0638048648834229, "end": **********.351112, "relative_end": **********.351112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.351814, "relative_start": 1.064507007598877, "end": **********.351814, "relative_end": **********.351814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.352165, "relative_start": 1.0648579597473145, "end": **********.352165, "relative_end": **********.352165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.352402, "relative_start": 1.0650949478149414, "end": **********.352402, "relative_end": **********.352402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.353118, "relative_start": 1.0658109188079834, "end": **********.353118, "relative_end": **********.353118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.353709, "relative_start": 1.0664019584655762, "end": **********.353709, "relative_end": **********.353709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.354354, "relative_start": 1.067046880722046, "end": **********.354354, "relative_end": **********.354354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d17cb0db54485d707113609802086895", "start": **********.354676, "relative_start": 1.067368984222412, "end": **********.354676, "relative_end": **********.354676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.35493, "relative_start": 1.0676229000091553, "end": **********.35493, "relative_end": **********.35493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::script", "start": **********.357159, "relative_start": 1.0698518753051758, "end": **********.357159, "relative_end": **********.357159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.358278, "relative_start": 1.0709710121154785, "end": **********.358278, "relative_end": **********.358278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.360435, "relative_start": 1.0731279850006104, "end": **********.360435, "relative_end": **********.360435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.360748, "relative_start": 1.0734410285949707, "end": **********.360748, "relative_end": **********.360748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.361882, "relative_start": 1.0745749473571777, "end": **********.361882, "relative_end": **********.361882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.362457, "relative_start": 1.0751500129699707, "end": **********.362457, "relative_end": **********.362457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.373531, "relative_start": 1.08622407913208, "end": **********.373531, "relative_end": **********.373531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.374012, "relative_start": 1.086704969406128, "end": **********.374012, "relative_end": **********.374012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.374838, "relative_start": 1.0875310897827148, "end": **********.374838, "relative_end": **********.374838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.375299, "relative_start": 1.0879919528961182, "end": **********.375299, "relative_end": **********.375299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.375657, "relative_start": 1.0883500576019287, "end": **********.375657, "relative_end": **********.375657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.376402, "relative_start": 1.089094877243042, "end": **********.376402, "relative_end": **********.376402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d2cfde89f704c31422aff2fae16ddb81", "start": **********.377283, "relative_start": 1.0899760723114014, "end": **********.377283, "relative_end": **********.377283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.377836, "relative_start": 1.090528964996338, "end": **********.377836, "relative_end": **********.377836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a5645d2a1f3c74251fc89224c575fed8", "start": **********.378897, "relative_start": 1.0915899276733398, "end": **********.378897, "relative_end": **********.378897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.380192, "relative_start": 1.0928850173950195, "end": **********.380192, "relative_end": **********.380192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::639d159f54869d7a8362974885dec505", "start": **********.381119, "relative_start": 1.0938119888305664, "end": **********.381119, "relative_end": **********.381119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/contact::partials.notification", "start": **********.383967, "relative_start": 1.0966598987579346, "end": **********.383967, "relative_end": **********.383967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.385318, "relative_start": 1.0980110168457031, "end": **********.385318, "relative_end": **********.385318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.title", "start": **********.386266, "relative_start": 1.098958969116211, "end": **********.386266, "relative_end": **********.386266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.actions", "start": **********.386721, "relative_start": 1.0994138717651367, "end": **********.386721, "relative_end": **********.386721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.386984, "relative_start": 1.0996770858764648, "end": **********.386984, "relative_end": **********.386984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.40171, "relative_start": 1.1144030094146729, "end": **********.40171, "relative_end": **********.40171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::orders.notification", "start": **********.418201, "relative_start": 1.1308939456939697, "end": **********.418201, "relative_end": **********.418201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "start": **********.419384, "relative_start": 1.1320769786834717, "end": **********.419384, "relative_end": **********.419384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.header.index", "start": **********.421366, "relative_start": 1.134058952331543, "end": **********.421366, "relative_end": **********.421366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::card.index", "start": **********.437237, "relative_start": 1.1499300003051758, "end": **********.437237, "relative_end": **********.437237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.437807, "relative_start": 1.1505000591278076, "end": **********.437807, "relative_end": **********.437807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.44137, "relative_start": 1.1540629863739014, "end": **********.44137, "relative_end": **********.44137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.44235, "relative_start": 1.1550428867340088, "end": **********.44235, "relative_end": **********.44235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.442973, "relative_start": 1.1556658744812012, "end": **********.442973, "relative_end": **********.442973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.443911, "relative_start": 1.1566040515899658, "end": **********.443911, "relative_end": **********.443911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.444398, "relative_start": 1.1570909023284912, "end": **********.444398, "relative_end": **********.444398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.445058, "relative_start": 1.1577510833740234, "end": **********.445058, "relative_end": **********.445058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3a4eb377d01a3c4bb09865b43ffbd313", "start": **********.445776, "relative_start": 1.1584689617156982, "end": **********.445776, "relative_end": **********.445776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.445982, "relative_start": 1.158674955368042, "end": **********.445982, "relative_end": **********.445982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.447461, "relative_start": 1.160153865814209, "end": **********.447461, "relative_end": **********.447461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d059faaba602d6895d68258ab3c890a6", "start": **********.448332, "relative_start": 1.161025047302246, "end": **********.448332, "relative_end": **********.448332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.item", "start": **********.448664, "relative_start": 1.1613569259643555, "end": **********.448664, "relative_end": **********.448664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3cb4601eb64a80dc01a3c268590a3c8", "start": **********.449373, "relative_start": 1.1620659828186035, "end": **********.449373, "relative_end": **********.449373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::dropdown.index", "start": **********.44962, "relative_start": 1.1623129844665527, "end": **********.44962, "relative_end": **********.44962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.450158, "relative_start": 1.162851095199585, "end": **********.450158, "relative_end": **********.450158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.450472, "relative_start": 1.1631650924682617, "end": **********.450472, "relative_end": **********.450472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.468321, "relative_start": 1.181014060974121, "end": **********.468321, "relative_end": **********.468321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.469136, "relative_start": 1.1818289756774902, "end": **********.469136, "relative_end": **********.469136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3b17f7ce9738894b58a8b70b9624457", "start": **********.470442, "relative_start": 1.1831350326538086, "end": **********.470442, "relative_end": **********.470442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.471353, "relative_start": 1.1840460300445557, "end": **********.471353, "relative_end": **********.471353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.472028, "relative_start": 1.1847209930419922, "end": **********.472028, "relative_end": **********.472028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0c6e6838aa476b78aace81114936689c", "start": **********.473731, "relative_start": 1.1864240169525146, "end": **********.473731, "relative_end": **********.473731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.474634, "relative_start": 1.1873269081115723, "end": **********.474634, "relative_end": **********.474634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.475, "relative_start": 1.1876928806304932, "end": **********.475, "relative_end": **********.475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.475444, "relative_start": 1.1881370544433594, "end": **********.475444, "relative_end": **********.475444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::37fae22c8e215ea2e54e69a5e3a007cc", "start": **********.476979, "relative_start": 1.1896719932556152, "end": **********.476979, "relative_end": **********.476979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.477766, "relative_start": 1.1904590129852295, "end": **********.477766, "relative_end": **********.477766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8916176d99d4ae2024cd36e11e35b821", "start": **********.47874, "relative_start": 1.1914329528808594, "end": **********.47874, "relative_end": **********.47874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.479565, "relative_start": 1.1922578811645508, "end": **********.479565, "relative_end": **********.479565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.479838, "relative_start": 1.192530870437622, "end": **********.479838, "relative_end": **********.479838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.480246, "relative_start": 1.192939043045044, "end": **********.480246, "relative_end": **********.480246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd27433a6607127acdaf6dc541ab2435", "start": **********.481577, "relative_start": 1.1942698955535889, "end": **********.481577, "relative_end": **********.481577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.483101, "relative_start": 1.195793867111206, "end": **********.483101, "relative_end": **********.483101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "start": **********.485172, "relative_start": 1.1978650093078613, "end": **********.485172, "relative_end": **********.485172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.486192, "relative_start": 1.1988849639892578, "end": **********.486192, "relative_end": **********.486192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.486598, "relative_start": 1.1992909908294678, "end": **********.486598, "relative_end": **********.486598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.487203, "relative_start": 1.1998958587646484, "end": **********.487203, "relative_end": **********.487203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::080b92e00b37bcc97c1cd249894494a2", "start": **********.489589, "relative_start": 1.2022819519042969, "end": **********.489589, "relative_end": **********.489589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.490666, "relative_start": 1.2033588886260986, "end": **********.490666, "relative_end": **********.490666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6a26943e77184871de1629f41c534094", "start": **********.49265, "relative_start": 1.2053430080413818, "end": **********.49265, "relative_end": **********.49265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.493635, "relative_start": 1.2063279151916504, "end": **********.493635, "relative_end": **********.493635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8f29f8012139c7a3eb6593c906e1db38", "start": **********.495116, "relative_start": 1.2078089714050293, "end": **********.495116, "relative_end": **********.495116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.49612, "relative_start": 1.208812952041626, "end": **********.49612, "relative_end": **********.49612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.496478, "relative_start": 1.2091710567474365, "end": **********.496478, "relative_end": **********.496478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.497425, "relative_start": 1.210118055343628, "end": **********.497425, "relative_end": **********.497425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4553f5b37130b2effba490dbdf5419d2", "start": **********.499765, "relative_start": 1.2124578952789307, "end": **********.499765, "relative_end": **********.499765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.500753, "relative_start": 1.2134459018707275, "end": **********.500753, "relative_end": **********.500753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59c947fc9b2121a5885d4f4e7b1242d8", "start": **********.502846, "relative_start": 1.2155389785766602, "end": **********.502846, "relative_end": **********.502846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.50385, "relative_start": 1.2165429592132568, "end": **********.50385, "relative_end": **********.50385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::76de3b53a2c2ccc2ffb093fedb19df44", "start": **********.504988, "relative_start": 1.2176809310913086, "end": **********.504988, "relative_end": **********.504988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.506285, "relative_start": 1.218977928161621, "end": **********.506285, "relative_end": **********.506285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "start": **********.507674, "relative_start": 1.2203669548034668, "end": **********.507674, "relative_end": **********.507674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.508647, "relative_start": 1.2213399410247803, "end": **********.508647, "relative_end": **********.508647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.509875, "relative_start": 1.2225680351257324, "end": **********.509875, "relative_end": **********.509875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.510818, "relative_start": 1.223510980606079, "end": **********.510818, "relative_end": **********.510818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.51238, "relative_start": 1.2250728607177734, "end": **********.51238, "relative_end": **********.51238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.513789, "relative_start": 1.2264819145202637, "end": **********.513789, "relative_end": **********.513789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::84f17fac377525e2e49f32058361220b", "start": **********.516695, "relative_start": 1.2293879985809326, "end": **********.516695, "relative_end": **********.516695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.51786, "relative_start": 1.2305529117584229, "end": **********.51786, "relative_end": **********.51786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5270fef4db64e6c2fedf42ea8ac88f25", "start": **********.519578, "relative_start": 1.2322709560394287, "end": **********.519578, "relative_end": **********.519578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.520292, "relative_start": 1.232985019683838, "end": **********.520292, "relative_end": **********.520292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c8617dee734f51544a3883923ddca6f", "start": **********.522152, "relative_start": 1.2348449230194092, "end": **********.522152, "relative_end": **********.522152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.523118, "relative_start": 1.2358109951019287, "end": **********.523118, "relative_end": **********.523118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3d3bfe5e8598abeb74083f6c26233cb5", "start": **********.524451, "relative_start": 1.2371439933776855, "end": **********.524451, "relative_end": **********.524451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.525547, "relative_start": 1.2382400035858154, "end": **********.525547, "relative_end": **********.525547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::35fe997a7b87ef55d749630606a50a1b", "start": **********.527408, "relative_start": 1.2401008605957031, "end": **********.527408, "relative_end": **********.527408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.531078, "relative_start": 1.2437710762023926, "end": **********.531078, "relative_end": **********.531078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a4f1583597dec7e67a8ae044f0915dbe", "start": **********.532991, "relative_start": 1.2456839084625244, "end": **********.532991, "relative_end": **********.532991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.533998, "relative_start": 1.2466909885406494, "end": **********.533998, "relative_end": **********.533998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c0cbd16b0cc2226ec5536610974ba3c3", "start": **********.536077, "relative_start": 1.248769998550415, "end": **********.536077, "relative_end": **********.536077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.537035, "relative_start": 1.2497279644012451, "end": **********.537035, "relative_end": **********.537035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.538955, "relative_start": 1.25164794921875, "end": **********.538955, "relative_end": **********.538955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.539907, "relative_start": 1.2525999546051025, "end": **********.539907, "relative_end": **********.539907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.540722, "relative_start": 1.2534148693084717, "end": **********.540722, "relative_end": **********.540722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc78b90963e9d9963376e0e829411cea", "start": **********.542611, "relative_start": 1.2553038597106934, "end": **********.542611, "relative_end": **********.542611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.543688, "relative_start": 1.2563810348510742, "end": **********.543688, "relative_end": **********.543688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.545451, "relative_start": 1.2581439018249512, "end": **********.545451, "relative_end": **********.545451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.547242, "relative_start": 1.259934902191162, "end": **********.547242, "relative_end": **********.547242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::481a833ebeb573258c941c925aa45f7b", "start": **********.549808, "relative_start": 1.2625010013580322, "end": **********.549808, "relative_end": **********.549808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.551648, "relative_start": 1.264340877532959, "end": **********.551648, "relative_end": **********.551648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b42bb0aa5fceca31ad61711414a614f0", "start": **********.554131, "relative_start": 1.2668240070343018, "end": **********.554131, "relative_end": **********.554131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.555597, "relative_start": 1.2682900428771973, "end": **********.555597, "relative_end": **********.555597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.556744, "relative_start": 1.2694370746612549, "end": **********.556744, "relative_end": **********.556744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.560507, "relative_start": 1.2732000350952148, "end": **********.560507, "relative_end": **********.560507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.561936, "relative_start": 1.2746288776397705, "end": **********.561936, "relative_end": **********.561936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.562342, "relative_start": 1.2750349044799805, "end": **********.562342, "relative_end": **********.562342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.562893, "relative_start": 1.2755858898162842, "end": **********.562893, "relative_end": **********.562893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::471e83668278198d730a7a3f4a475d45", "start": **********.564993, "relative_start": 1.2776858806610107, "end": **********.564993, "relative_end": **********.564993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.566052, "relative_start": 1.2787449359893799, "end": **********.566052, "relative_end": **********.566052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.56707, "relative_start": 1.2797629833221436, "end": **********.56707, "relative_end": **********.56707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.567745, "relative_start": 1.28043794631958, "end": **********.567745, "relative_end": **********.567745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "start": **********.56956, "relative_start": 1.2822530269622803, "end": **********.56956, "relative_end": **********.56956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.570445, "relative_start": 1.2831380367279053, "end": **********.570445, "relative_end": **********.570445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.570768, "relative_start": 1.283461093902588, "end": **********.570768, "relative_end": **********.570768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.571232, "relative_start": 1.2839250564575195, "end": **********.571232, "relative_end": **********.571232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::70b8df706e60982a72f15e9e2d486203", "start": **********.572046, "relative_start": 1.2847390174865723, "end": **********.572046, "relative_end": **********.572046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.572634, "relative_start": 1.2853269577026367, "end": **********.572634, "relative_end": **********.572634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acb69140835a74210411469faeab3034", "start": **********.574601, "relative_start": 1.2872939109802246, "end": **********.574601, "relative_end": **********.574601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.575556, "relative_start": 1.2882490158081055, "end": **********.575556, "relative_end": **********.575556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.575887, "relative_start": 1.2885799407958984, "end": **********.575887, "relative_end": **********.575887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.57635, "relative_start": 1.2890429496765137, "end": **********.57635, "relative_end": **********.57635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19cd49cd69455e40dc223df6b4eaf954", "start": **********.577952, "relative_start": 1.290644884109497, "end": **********.577952, "relative_end": **********.577952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.578843, "relative_start": 1.2915360927581787, "end": **********.578843, "relative_end": **********.578843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.579643, "relative_start": 1.2923359870910645, "end": **********.579643, "relative_end": **********.579643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2979b72aeeca0047ecdecc3ad66e7e16", "start": **********.581086, "relative_start": 1.293778896331787, "end": **********.581086, "relative_end": **********.581086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.582166, "relative_start": 1.2948589324951172, "end": **********.582166, "relative_end": **********.582166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.583102, "relative_start": 1.29579496383667, "end": **********.583102, "relative_end": **********.583102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3c40febd70fcdc245d99ae7cd02cface", "start": **********.58492, "relative_start": 1.2976129055023193, "end": **********.58492, "relative_end": **********.58492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.58585, "relative_start": 1.2985429763793945, "end": **********.58585, "relative_end": **********.58585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.587085, "relative_start": 1.2997779846191406, "end": **********.587085, "relative_end": **********.587085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.587881, "relative_start": 1.3005740642547607, "end": **********.587881, "relative_end": **********.587881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.588894, "relative_start": 1.3015868663787842, "end": **********.588894, "relative_end": **********.588894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.589464, "relative_start": 1.302156925201416, "end": **********.589464, "relative_end": **********.589464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d4eb4544a5328bea40b7b01743b8f82", "start": **********.590549, "relative_start": 1.3032419681549072, "end": **********.590549, "relative_end": **********.590549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.591134, "relative_start": 1.3038270473480225, "end": **********.591134, "relative_end": **********.591134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.591861, "relative_start": 1.3045539855957031, "end": **********.591861, "relative_end": **********.591861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d7ced212b797c29086a7922a858f3070", "start": **********.593612, "relative_start": 1.306304931640625, "end": **********.593612, "relative_end": **********.593612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.594471, "relative_start": 1.307163953781128, "end": **********.594471, "relative_end": **********.594471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.59477, "relative_start": 1.3074629306793213, "end": **********.59477, "relative_end": **********.59477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.595199, "relative_start": 1.307892084121704, "end": **********.595199, "relative_end": **********.595199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.596316, "relative_start": 1.309009075164795, "end": **********.596316, "relative_end": **********.596316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.597252, "relative_start": 1.3099448680877686, "end": **********.597252, "relative_end": **********.597252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.59765, "relative_start": 1.3103430271148682, "end": **********.59765, "relative_end": **********.59765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.598195, "relative_start": 1.3108880519866943, "end": **********.598195, "relative_end": **********.598195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::90ccac5c8bbb25741ef262bfd81c7551", "start": **********.599352, "relative_start": 1.3120448589324951, "end": **********.599352, "relative_end": **********.599352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.599959, "relative_start": 1.3126518726348877, "end": **********.599959, "relative_end": **********.599959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.60111, "relative_start": 1.31380295753479, "end": **********.60111, "relative_end": **********.60111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.601974, "relative_start": 1.314666986465454, "end": **********.601974, "relative_end": **********.601974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.602705, "relative_start": 1.3153979778289795, "end": **********.602705, "relative_end": **********.602705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c969038219bd5c599f1ca2d81401cea", "start": **********.604162, "relative_start": 1.3168549537658691, "end": **********.604162, "relative_end": **********.604162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.604931, "relative_start": 1.3176240921020508, "end": **********.604931, "relative_end": **********.604931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.605921, "relative_start": 1.3186140060424805, "end": **********.605921, "relative_end": **********.605921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8c52d9b1ef0685ec10fdc3e877751e02", "start": **********.607026, "relative_start": 1.3197190761566162, "end": **********.607026, "relative_end": **********.607026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.608004, "relative_start": 1.3206970691680908, "end": **********.608004, "relative_end": **********.608004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.609001, "relative_start": 1.3216938972473145, "end": **********.609001, "relative_end": **********.609001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42668be6e8e5266862c6994eaa88bb55", "start": **********.611162, "relative_start": 1.323854923248291, "end": **********.611162, "relative_end": **********.611162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.612047, "relative_start": 1.324739933013916, "end": **********.612047, "relative_end": **********.612047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.612387, "relative_start": 1.3250799179077148, "end": **********.612387, "relative_end": **********.612387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.612886, "relative_start": 1.3255789279937744, "end": **********.612886, "relative_end": **********.612886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.61366, "relative_start": 1.3263530731201172, "end": **********.61366, "relative_end": **********.61366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "start": **********.615603, "relative_start": 1.3282959461212158, "end": **********.615603, "relative_end": **********.615603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.617082, "relative_start": 1.329775094985962, "end": **********.617082, "relative_end": **********.617082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d41a7757b46012fb4a0d6634d04a1e0", "start": **********.618691, "relative_start": 1.3313839435577393, "end": **********.618691, "relative_end": **********.618691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.619403, "relative_start": 1.3320958614349365, "end": **********.619403, "relative_end": **********.619403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.620316, "relative_start": 1.3330090045928955, "end": **********.620316, "relative_end": **********.620316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.620964, "relative_start": 1.3336570262908936, "end": **********.620964, "relative_end": **********.620964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.621747, "relative_start": 1.334439992904663, "end": **********.621747, "relative_end": **********.621747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.622746, "relative_start": 1.3354389667510986, "end": **********.622746, "relative_end": **********.622746, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.623349, "relative_start": 1.3360419273376465, "end": **********.623349, "relative_end": **********.623349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::navbar.badge-count", "start": **********.623704, "relative_start": 1.3363969326019287, "end": **********.623704, "relative_end": **********.623704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.624195, "relative_start": 1.336888074874878, "end": **********.624195, "relative_end": **********.624195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::613233f0072612a02c74dd1699c0b74c", "start": **********.625452, "relative_start": 1.3381450176239014, "end": **********.625452, "relative_end": **********.625452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.626428, "relative_start": 1.339120864868164, "end": **********.626428, "relative_end": **********.626428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16225ede2ef5cc17292fd2eb9026fc80", "start": **********.627713, "relative_start": 1.3404059410095215, "end": **********.627713, "relative_end": **********.627713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.628685, "relative_start": 1.3413779735565186, "end": **********.628685, "relative_end": **********.628685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.629449, "relative_start": 1.34214186668396, "end": **********.629449, "relative_end": **********.629449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46010cb1cb88bb5ead5d94603a4a3d16", "start": **********.630749, "relative_start": 1.3434419631958008, "end": **********.630749, "relative_end": **********.630749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.631824, "relative_start": 1.3445169925689697, "end": **********.631824, "relative_end": **********.631824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.632744, "relative_start": 1.3454370498657227, "end": **********.632744, "relative_end": **********.632744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::34e3d89351b1208b7f313125eec52879", "start": **********.634743, "relative_start": 1.3474359512329102, "end": **********.634743, "relative_end": **********.634743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.635775, "relative_start": 1.3484680652618408, "end": **********.635775, "relative_end": **********.635775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::67034900569133b2c83b32da3dd4f5e5", "start": **********.637046, "relative_start": 1.3497390747070312, "end": **********.637046, "relative_end": **********.637046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.637934, "relative_start": 1.3506269454956055, "end": **********.637934, "relative_end": **********.637934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.639531, "relative_start": 1.3522238731384277, "end": **********.639531, "relative_end": **********.639531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.640206, "relative_start": 1.3528990745544434, "end": **********.640206, "relative_end": **********.640206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.641018, "relative_start": 1.3537108898162842, "end": **********.641018, "relative_end": **********.641018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cf41524c2db4e8ac4f30aba28550db55", "start": **********.642318, "relative_start": 1.355010986328125, "end": **********.642318, "relative_end": **********.642318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.642907, "relative_start": 1.3555998802185059, "end": **********.642907, "relative_end": **********.642907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.64363, "relative_start": 1.356323003768921, "end": **********.64363, "relative_end": **********.64363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c33cfbd01dd1d76718fcd68287a40728", "start": **********.644825, "relative_start": 1.357517957687378, "end": **********.644825, "relative_end": **********.644825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.645496, "relative_start": 1.3581888675689697, "end": **********.645496, "relative_end": **********.645496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d623715926c24f9fbc8a4b72c106d5d", "start": **********.647117, "relative_start": 1.3598098754882812, "end": **********.647117, "relative_end": **********.647117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.648487, "relative_start": 1.361180067062378, "end": **********.648487, "relative_end": **********.648487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6c50fc55276d93f8ed03f5c85273b6cc", "start": **********.652322, "relative_start": 1.3650150299072266, "end": **********.652322, "relative_end": **********.652322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.653341, "relative_start": 1.3660340309143066, "end": **********.653341, "relative_end": **********.653341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::084d2b43c9ab4b881d9b34a15580aa2d", "start": **********.65524, "relative_start": 1.3679330348968506, "end": **********.65524, "relative_end": **********.65524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.656227, "relative_start": 1.368920087814331, "end": **********.656227, "relative_end": **********.656227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.657953, "relative_start": 1.3706459999084473, "end": **********.657953, "relative_end": **********.657953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.658945, "relative_start": 1.3716380596160889, "end": **********.658945, "relative_end": **********.658945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7db8cad89359963c1e9aa8fcc6c89817", "start": **********.66046, "relative_start": 1.3731529712677002, "end": **********.66046, "relative_end": **********.66046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.661381, "relative_start": 1.3740739822387695, "end": **********.661381, "relative_end": **********.661381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.66214, "relative_start": 1.3748328685760498, "end": **********.66214, "relative_end": **********.66214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7a6e3d0dfcd673b5659893aa4dd54e33", "start": **********.663712, "relative_start": 1.3764050006866455, "end": **********.663712, "relative_end": **********.663712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.664906, "relative_start": 1.3775990009307861, "end": **********.664906, "relative_end": **********.664906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.665886, "relative_start": 1.3785789012908936, "end": **********.665886, "relative_end": **********.665886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::325be2a8c3ca3843efa76c03adaee1dc", "start": **********.667884, "relative_start": 1.3805770874023438, "end": **********.667884, "relative_end": **********.667884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.668797, "relative_start": 1.3814899921417236, "end": **********.668797, "relative_end": **********.668797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e12a669ffa0346a27198bed32e63b7ba", "start": **********.670353, "relative_start": 1.3830459117889404, "end": **********.670353, "relative_end": **********.670353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.671025, "relative_start": 1.3837180137634277, "end": **********.671025, "relative_end": **********.671025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9d0b20db301db9a47503a93a879bb206", "start": **********.6723, "relative_start": 1.384993076324463, "end": **********.6723, "relative_end": **********.6723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.673167, "relative_start": 1.3858599662780762, "end": **********.673167, "relative_end": **********.673167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b985af7bcdacbeac70eaf3979ad19f5a", "start": **********.674384, "relative_start": 1.3870770931243896, "end": **********.674384, "relative_end": **********.674384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.675312, "relative_start": 1.388005018234253, "end": **********.675312, "relative_end": **********.675312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a0bb1d43b71cff86abe626fd376492e9", "start": **********.676555, "relative_start": 1.3892478942871094, "end": **********.676555, "relative_end": **********.676555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.677518, "relative_start": 1.3902108669281006, "end": **********.677518, "relative_end": **********.677518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e25f2b305e6de46c04f91fa1ce50f68f", "start": **********.678763, "relative_start": 1.391455888748169, "end": **********.678763, "relative_end": **********.678763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.679618, "relative_start": 1.3923108577728271, "end": **********.679618, "relative_end": **********.679618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69152f707ea1358f8997b77a28e38a6f", "start": **********.680903, "relative_start": 1.3935959339141846, "end": **********.680903, "relative_end": **********.680903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.682248, "relative_start": 1.3949410915374756, "end": **********.682248, "relative_end": **********.682248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b85eba35d3b7929c2988678b725baebf", "start": **********.684747, "relative_start": 1.397439956665039, "end": **********.684747, "relative_end": **********.684747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.686222, "relative_start": 1.3989150524139404, "end": **********.686222, "relative_end": **********.686222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f3d3b83c612f68036b4d79d53ae851e", "start": **********.68766, "relative_start": 1.400352954864502, "end": **********.68766, "relative_end": **********.68766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.688629, "relative_start": 1.4013218879699707, "end": **********.688629, "relative_end": **********.688629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.689428, "relative_start": 1.4021210670471191, "end": **********.689428, "relative_end": **********.689428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f143d1296cea16d82e2c87956e445593", "start": **********.691248, "relative_start": 1.4039409160614014, "end": **********.691248, "relative_end": **********.691248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.692157, "relative_start": 1.4048500061035156, "end": **********.692157, "relative_end": **********.692157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8cae4e5056b67c6778a54389a62ac7a0", "start": **********.693427, "relative_start": 1.4061200618743896, "end": **********.693427, "relative_end": **********.693427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.694295, "relative_start": 1.4069879055023193, "end": **********.694295, "relative_end": **********.694295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d5e509b6eb9084ec382ec05ccab41d1a", "start": **********.695505, "relative_start": 1.4081978797912598, "end": **********.695505, "relative_end": **********.695505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.696384, "relative_start": 1.4090769290924072, "end": **********.696384, "relative_end": **********.696384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.697197, "relative_start": 1.4098899364471436, "end": **********.697197, "relative_end": **********.697197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e81a46563ed9378aa4d9a4fcb55e743e", "start": **********.699066, "relative_start": 1.4117588996887207, "end": **********.699066, "relative_end": **********.699066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.700055, "relative_start": 1.412747859954834, "end": **********.700055, "relative_end": **********.700055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b33d20952e90e5c4a596ff58ad010448", "start": **********.701451, "relative_start": 1.4141440391540527, "end": **********.701451, "relative_end": **********.701451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.702379, "relative_start": 1.415071964263916, "end": **********.702379, "relative_end": **********.702379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.703153, "relative_start": 1.4158458709716797, "end": **********.703153, "relative_end": **********.703153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86b7e33bd2198279086ebb1f21c0e2cc", "start": **********.703983, "relative_start": 1.4166760444641113, "end": **********.703983, "relative_end": **********.703983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.704564, "relative_start": 1.4172570705413818, "end": **********.704564, "relative_end": **********.704564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.705281, "relative_start": 1.4179739952087402, "end": **********.705281, "relative_end": **********.705281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da3c3de008e5793cbbdad005d78f49b1", "start": **********.706514, "relative_start": 1.4192068576812744, "end": **********.706514, "relative_end": **********.706514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.7078, "relative_start": 1.4204928874969482, "end": **********.7078, "relative_end": **********.7078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.708202, "relative_start": 1.4208948612213135, "end": **********.708202, "relative_end": **********.708202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.709263, "relative_start": 1.4219560623168945, "end": **********.709263, "relative_end": **********.709263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.709617, "relative_start": 1.4223098754882812, "end": **********.709617, "relative_end": **********.709617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.710505, "relative_start": 1.4231979846954346, "end": **********.710505, "relative_end": **********.710505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.license-invalid", "start": **********.711076, "relative_start": 1.4237689971923828, "end": **********.711076, "relative_end": **********.711076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.711726, "relative_start": 1.4244189262390137, "end": **********.711726, "relative_end": **********.711726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::system.partials.license-activation-modal", "start": **********.712257, "relative_start": 1.4249498844146729, "end": **********.712257, "relative_end": **********.712257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::license.form", "start": **********.712777, "relative_start": 1.4254698753356934, "end": **********.712777, "relative_end": **********.712777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::alert", "start": **********.713503, "relative_start": 1.4261958599090576, "end": **********.713503, "relative_end": **********.713503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27ec08f706fece52ef1cc0ec5563cef9", "start": **********.715234, "relative_start": 1.427927017211914, "end": **********.715234, "relative_end": **********.715234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.716213, "relative_start": 1.428905963897705, "end": **********.716213, "relative_end": **********.716213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.717985, "relative_start": 1.430677890777588, "end": **********.717985, "relative_end": **********.717985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.718747, "relative_start": 1.4314398765563965, "end": **********.718747, "relative_end": **********.718747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.719075, "relative_start": 1.4317679405212402, "end": **********.719075, "relative_end": **********.719075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.71942, "relative_start": 1.4321129322052002, "end": **********.71942, "relative_end": **********.71942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.719778, "relative_start": 1.4324710369110107, "end": **********.719778, "relative_end": **********.719778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.720559, "relative_start": 1.4332518577575684, "end": **********.720559, "relative_end": **********.720559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.helper-text", "start": **********.720934, "relative_start": 1.4336268901824951, "end": **********.720934, "relative_end": **********.720934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.721186, "relative_start": 1.4338788986206055, "end": **********.721186, "relative_end": **********.721186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.721514, "relative_start": 1.4342069625854492, "end": **********.721514, "relative_end": **********.721514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "start": **********.721886, "relative_start": 1.4345788955688477, "end": **********.721886, "relative_end": **********.721886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.form.checkbox", "start": **********.722277, "relative_start": 1.4349699020385742, "end": **********.722277, "relative_end": **********.722277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.722797, "relative_start": 1.4354898929595947, "end": **********.722797, "relative_end": **********.722797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.723158, "relative_start": 1.4358508586883545, "end": **********.723158, "relative_end": **********.723158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.723829, "relative_start": 1.4365220069885254, "end": **********.723829, "relative_end": **********.723829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.724176, "relative_start": 1.4368689060211182, "end": **********.724176, "relative_end": **********.724176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.724566, "relative_start": 1.4372589588165283, "end": **********.724566, "relative_end": **********.724566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.725256, "relative_start": 1.4379489421844482, "end": **********.725256, "relative_end": **********.725256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.725853, "relative_start": 1.4385459423065186, "end": **********.725853, "relative_end": **********.725853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6f3b10173cc6f5c541f27080145e1a40", "start": **********.726787, "relative_start": 1.4394800662994385, "end": **********.726787, "relative_end": **********.726787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.727008, "relative_start": 1.4397010803222656, "end": **********.727008, "relative_end": **********.727008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.727734, "relative_start": 1.4404270648956299, "end": **********.727734, "relative_end": **********.727734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.728175, "relative_start": 1.4408679008483887, "end": **********.728175, "relative_end": **********.728175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.728504, "relative_start": 1.4411969184875488, "end": **********.728504, "relative_end": **********.728504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.index", "start": **********.728815, "relative_start": 1.4415080547332764, "end": **********.728815, "relative_end": **********.728815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53362b6227831afe8e4d7d3436ab607f", "start": **********.729612, "relative_start": 1.442305088043213, "end": **********.729612, "relative_end": **********.729612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e76aef074ac8ea84c711b8437720a22", "start": **********.730644, "relative_start": 1.4433369636535645, "end": **********.730644, "relative_end": **********.730644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::04edbddbda254d131a3439b11c880f12", "start": **********.73177, "relative_start": 1.4444630146026611, "end": **********.73177, "relative_end": **********.73177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.732364, "relative_start": 1.4450569152832031, "end": **********.732364, "relative_end": **********.732364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::custom-template", "start": **********.733253, "relative_start": 1.4459459781646729, "end": **********.733253, "relative_end": **********.733253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.733658, "relative_start": 1.4463510513305664, "end": **********.733658, "relative_end": **********.733658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.734591, "relative_start": 1.4472839832305908, "end": **********.734591, "relative_end": **********.734591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::loading", "start": **********.734916, "relative_start": 1.4476089477539062, "end": **********.734916, "relative_end": **********.734916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.text-input", "start": **********.735366, "relative_start": 1.44805908203125, "end": **********.735366, "relative_end": **********.735366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.label", "start": **********.73615, "relative_start": 1.448843002319336, "end": **********.73615, "relative_end": **********.73615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.error", "start": **********.736561, "relative_start": 1.449254035949707, "end": **********.736561, "relative_end": **********.736561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form-group", "start": **********.7369, "relative_start": 1.4495930671691895, "end": **********.7369, "relative_end": **********.7369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::form.checkbox", "start": **********.73732, "relative_start": 1.4500129222869873, "end": **********.73732, "relative_end": **********.73732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.73888, "relative_start": 1.4515728950500488, "end": **********.73888, "relative_end": **********.73888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::button", "start": **********.739505, "relative_start": 1.4521980285644531, "end": **********.739505, "relative_end": **********.739505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.740098, "relative_start": 1.4527909755706787, "end": **********.740098, "relative_end": **********.740098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.740806, "relative_start": 1.4534990787506104, "end": **********.740806, "relative_end": **********.740806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.741203, "relative_start": 1.4538960456848145, "end": **********.741203, "relative_end": **********.741203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::debug-badge", "start": **********.952892, "relative_start": 1.6655850410461426, "end": **********.952892, "relative_end": **********.952892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.953646, "relative_start": 1.6663389205932617, "end": **********.953646, "relative_end": **********.953646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.954328, "relative_start": 1.6670210361480713, "end": **********.954328, "relative_end": **********.954328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.954969, "relative_start": 1.6676619052886963, "end": **********.954969, "relative_end": **********.954969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::93ebde3601860db875cfe9a96164bda6", "start": **********.955964, "relative_start": 1.6686570644378662, "end": **********.955964, "relative_end": **********.955964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.956509, "relative_start": 1.6692020893096924, "end": **********.956509, "relative_end": **********.956509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.action", "start": **********.957341, "relative_start": 1.6700339317321777, "end": **********.957341, "relative_end": **********.957341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.alert", "start": **********.957919, "relative_start": 1.67061185836792, "end": **********.957919, "relative_end": **********.957919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal.close-button", "start": **********.958516, "relative_start": 1.6712088584899902, "end": **********.958516, "relative_end": **********.958516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::16c15d36d71c18d83a7e9e7e1b68a92b", "start": **********.959065, "relative_start": 1.6717579364776611, "end": **********.959065, "relative_end": **********.959065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::modal", "start": **********.959597, "relative_start": 1.6722900867462158, "end": **********.959597, "relative_end": **********.959597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: 8def1252668913628243c4d363bee1ef::layouts.base", "start": **********.960371, "relative_start": 1.6730639934539795, "end": **********.960371, "relative_end": **********.960371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.96109, "relative_start": 1.6737830638885498, "end": **********.96109, "relative_end": **********.96109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.963564, "relative_start": 1.6762568950653076, "end": **********.963564, "relative_end": **********.963564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.966391, "relative_start": 1.679084062576294, "end": **********.966391, "relative_end": **********.966391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.967945, "relative_start": 1.680638074874878, "end": **********.967945, "relative_end": **********.967945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.968915, "relative_start": 1.681607961654663, "end": **********.968915, "relative_end": **********.968915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 75241736, "peak_usage_str": "72MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "adawliahshop.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 384, "nb_templates": 384, "templates": [{"name": "1x core/table::bulk-changes", "param_count": null, "params": [], "start": **********.296673, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/bulk-changes.blade.phpcore/table::bulk-changes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-changes.blade.php&line=1", "ajax": false, "filename": "bulk-changes.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-changes"}, {"name": "8x 8def1252668913628243c4d363bee1ef::dropdown.item", "param_count": null, "params": [], "start": **********.299551, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/dropdown/item.blade.php8def1252668913628243c4d363bee1ef::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 8, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.item"}, {"name": "1x __components::53dbbce7846c20f5734d935076bf04ca", "param_count": null, "params": [], "start": **********.302001, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/53dbbce7846c20f5734d935076bf04ca.blade.php__components::53dbbce7846c20f5734d935076bf04ca", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F53dbbce7846c20f5734d935076bf04ca.blade.php&line=1", "ajax": false, "filename": "53dbbce7846c20f5734d935076bf04ca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53dbbce7846c20f5734d935076bf04ca"}, {"name": "1x core/table::table-info", "param_count": null, "params": [], "start": **********.306107, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table-info"}, {"name": "2x __components::c33cfbd01dd1d76718fcd68287a40728", "param_count": null, "params": [], "start": **********.30693, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/c33cfbd01dd1d76718fcd68287a40728.blade.php__components::c33cfbd01dd1d76718fcd68287a40728", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fc33cfbd01dd1d76718fcd68287a40728.blade.php&line=1", "ajax": false, "filename": "c33cfbd01dd1d76718fcd68287a40728.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::c33cfbd01dd1d76718fcd68287a40728"}, {"name": "1x 8def1252668913628243c4d363bee1ef::badge", "param_count": null, "params": [], "start": **********.307499, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/badge.blade.php8def1252668913628243c4d363bee1ef::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::badge"}, {"name": "1x core/table::table", "param_count": null, "params": [], "start": **********.310643, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/table.blade.phpcore/table::table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table"}, {"name": "1x core/table::base-table", "param_count": null, "params": [], "start": **********.310923, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/base-table.blade.phpcore/table::base-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbase-table.blade.php&line=1", "ajax": false, "filename": "base-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::base-table"}, {"name": "14x 8def1252668913628243c4d363bee1ef::button", "param_count": null, "params": [], "start": **********.313333, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/button.blade.php8def1252668913628243c4d363bee1ef::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 14, "name_original": "8def1252668913628243c4d363bee1ef::button"}, {"name": "1x __components::14ad31fb3af14d3ba24d3c578af35e73", "param_count": null, "params": [], "start": **********.314693, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/14ad31fb3af14d3ba24d3c578af35e73.blade.php__components::14ad31fb3af14d3ba24d3c578af35e73", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F14ad31fb3af14d3ba24d3c578af35e73.blade.php&line=1", "ajax": false, "filename": "14ad31fb3af14d3ba24d3c578af35e73.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::14ad31fb3af14d3ba24d3c578af35e73"}, {"name": "1x core/table::filter", "param_count": null, "params": [], "start": **********.316859, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/filter.blade.phpcore/table::filter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ffilter.blade.php&line=1", "ajax": false, "filename": "filter.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::filter"}, {"name": "4x 8def1252668913628243c4d363bee1ef::form.select", "param_count": null, "params": [], "start": **********.318331, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/select.blade.php8def1252668913628243c4d363bee1ef::form.select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 4, "name_original": "8def1252668913628243c4d363bee1ef::form.select"}, {"name": "11x 8def1252668913628243c4d363bee1ef::form-group", "param_count": null, "params": [], "start": **********.319911, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form-group.blade.php8def1252668913628243c4d363bee1ef::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 11, "name_original": "8def1252668913628243c4d363bee1ef::form-group"}, {"name": "1x __components::d7b9d721822c50540453b6fa0f4bd081", "param_count": null, "params": [], "start": **********.322286, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/d7b9d721822c50540453b6fa0f4bd081.blade.php__components::d7b9d721822c50540453b6fa0f4bd081", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fd7b9d721822c50540453b6fa0f4bd081.blade.php&line=1", "ajax": false, "filename": "d7b9d721822c50540453b6fa0f4bd081.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7b9d721822c50540453b6fa0f4bd081"}, {"name": "2x __components::0c8728926b3975e33a051ebb6ef68e5d", "param_count": null, "params": [], "start": **********.32725, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/0c8728926b3975e33a051ebb6ef68e5d.blade.php__components::0c8728926b3975e33a051ebb6ef68e5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F0c8728926b3975e33a051ebb6ef68e5d.blade.php&line=1", "ajax": false, "filename": "0c8728926b3975e33a051ebb6ef68e5d.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0c8728926b3975e33a051ebb6ef68e5d"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.index", "param_count": null, "params": [], "start": **********.327831, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/index.blade.php8def1252668913628243c4d363bee1ef::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.index"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.body.index", "param_count": null, "params": [], "start": **********.328166, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/card/body/index.blade.php8def1252668913628243c4d363bee1ef::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.body.index"}, {"name": "4x 8def1252668913628243c4d363bee1ef::card.index", "param_count": null, "params": [], "start": **********.328453, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/card/index.blade.php8def1252668913628243c4d363bee1ef::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "8def1252668913628243c4d363bee1ef::card.index"}, {"name": "1x core/table::bulk-action", "param_count": null, "params": [], "start": **********.33026, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/bulk-action.blade.phpcore/table::bulk-action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-action.blade.php&line=1", "ajax": false, "filename": "bulk-action.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-action"}, {"name": "3x 8def1252668913628243c4d363bee1ef::dropdown.index", "param_count": null, "params": [], "start": **********.332417, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/dropdown/index.blade.php8def1252668913628243c4d363bee1ef::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::dropdown.index"}, {"name": "2x __components::6f3b10173cc6f5c541f27080145e1a40", "param_count": null, "params": [], "start": **********.335376, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/6f3b10173cc6f5c541f27080145e1a40.blade.php__components::6f3b10173cc6f5c541f27080145e1a40", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F6f3b10173cc6f5c541f27080145e1a40.blade.php&line=1", "ajax": false, "filename": "6f3b10173cc6f5c541f27080145e1a40.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6f3b10173cc6f5c541f27080145e1a40"}, {"name": "1x __components::42e1966f95bce065f65d4b22e53f3772", "param_count": null, "params": [], "start": **********.3362, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/42e1966f95bce065f65d4b22e53f3772.blade.php__components::42e1966f95bce065f65d4b22e53f3772", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F42e1966f95bce065f65d4b22e53f3772.blade.php&line=1", "ajax": false, "filename": "42e1966f95bce065f65d4b22e53f3772.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42e1966f95bce065f65d4b22e53f3772"}, {"name": "3x 8def1252668913628243c4d363bee1ef::card.header.index", "param_count": null, "params": [], "start": **********.338572, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/card/header/index.blade.php8def1252668913628243c4d363bee1ef::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "8def1252668913628243c4d363bee1ef::card.header.index"}, {"name": "1x core/table::modal", "param_count": null, "params": [], "start": **********.340596, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/modal.blade.phpcore/table::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::modal"}, {"name": "6x 8def1252668913628243c4d363bee1ef::modal.action", "param_count": null, "params": [], "start": **********.341483, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/modal/action.blade.php8def1252668913628243c4d363bee1ef::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 6, "name_original": "8def1252668913628243c4d363bee1ef::modal.action"}, {"name": "6x 8def1252668913628243c4d363bee1ef::modal.alert", "param_count": null, "params": [], "start": **********.342214, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/modal/alert.blade.php8def1252668913628243c4d363bee1ef::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 6, "name_original": "8def1252668913628243c4d363bee1ef::modal.alert"}, {"name": "10x 8def1252668913628243c4d363bee1ef::modal.close-button", "param_count": null, "params": [], "start": **********.342889, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/modal/close-button.blade.php8def1252668913628243c4d363bee1ef::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::modal.close-button"}, {"name": "4x __components::d17cb0db54485d707113609802086895", "param_count": null, "params": [], "start": **********.34359, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/d17cb0db54485d707113609802086895.blade.php__components::d17cb0db54485d707113609802086895", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fd17cb0db54485d707113609802086895.blade.php&line=1", "ajax": false, "filename": "d17cb0db54485d707113609802086895.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::d17cb0db54485d707113609802086895"}, {"name": "10x 8def1252668913628243c4d363bee1ef::modal", "param_count": null, "params": [], "start": **********.344169, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/modal.blade.php8def1252668913628243c4d363bee1ef::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 10, "name_original": "8def1252668913628243c4d363bee1ef::modal"}, {"name": "1x core/table::script", "param_count": null, "params": [], "start": **********.35714, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/table/resources/views/script.blade.phpcore/table::script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::script"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.358259, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.360414, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.360728, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::3a4eb377d01a3c4bb09865b43ffbd313", "param_count": null, "params": [], "start": **********.361864, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/3a4eb377d01a3c4bb09865b43ffbd313.blade.php__components::3a4eb377d01a3c4bb09865b43ffbd313", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F3a4eb377d01a3c4bb09865b43ffbd313.blade.php&line=1", "ajax": false, "filename": "3a4eb377d01a3c4bb09865b43ffbd313.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::3a4eb377d01a3c4bb09865b43ffbd313"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.362439, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.373507, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.text-input", "param_count": null, "params": [], "start": **********.373992, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/text-input.blade.php8def1252668913628243c4d363bee1ef::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.text-input"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.label", "param_count": null, "params": [], "start": **********.374819, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/label.blade.php8def1252668913628243c4d363bee1ef::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.label"}, {"name": "5x 8def1252668913628243c4d363bee1ef::form.error", "param_count": null, "params": [], "start": **********.37528, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/error.blade.php8def1252668913628243c4d363bee1ef::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 5, "name_original": "8def1252668913628243c4d363bee1ef::form.error"}, {"name": "1x __components::d2cfde89f704c31422aff2fae16ddb81", "param_count": null, "params": [], "start": **********.377265, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/d2cfde89f704c31422aff2fae16ddb81.blade.php__components::d2cfde89f704c31422aff2fae16ddb81", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fd2cfde89f704c31422aff2fae16ddb81.blade.php&line=1", "ajax": false, "filename": "d2cfde89f704c31422aff2fae16ddb81.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d2cfde89f704c31422aff2fae16ddb81"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.377817, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::a5645d2a1f3c74251fc89224c575fed8", "param_count": null, "params": [], "start": **********.378878, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/a5645d2a1f3c74251fc89224c575fed8.blade.php__components::a5645d2a1f3c74251fc89224c575fed8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fa5645d2a1f3c74251fc89224c575fed8.blade.php&line=1", "ajax": false, "filename": "a5645d2a1f3c74251fc89224c575fed8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a5645d2a1f3c74251fc89224c575fed8"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.380172, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::639d159f54869d7a8362974885dec505", "param_count": null, "params": [], "start": **********.381087, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/639d159f54869d7a8362974885dec505.blade.php__components::639d159f54869d7a8362974885dec505", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F639d159f54869d7a8362974885dec505.blade.php&line=1", "ajax": false, "filename": "639d159f54869d7a8362974885dec505.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::639d159f54869d7a8362974885dec505"}, {"name": "1x plugins/contact::partials.notification", "param_count": null, "params": [], "start": **********.383941, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/plugins/contact/resources/views/partials/notification.blade.phpplugins/contact::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fcontact%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/contact::partials.notification"}, {"name": "3x __components::cf41524c2db4e8ac4f30aba28550db55", "param_count": null, "params": [], "start": **********.385298, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/cf41524c2db4e8ac4f30aba28550db55.blade.php__components::cf41524c2db4e8ac4f30aba28550db55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fcf41524c2db4e8ac4f30aba28550db55.blade.php&line=1", "ajax": false, "filename": "cf41524c2db4e8ac4f30aba28550db55.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::cf41524c2db4e8ac4f30aba28550db55"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.title", "param_count": null, "params": [], "start": **********.386247, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/card/title.blade.php8def1252668913628243c4d363bee1ef::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.title"}, {"name": "1x 8def1252668913628243c4d363bee1ef::card.actions", "param_count": null, "params": [], "start": **********.386702, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/card/actions.blade.php8def1252668913628243c4d363bee1ef::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::card.actions"}, {"name": "1x plugins/ecommerce::orders.notification", "param_count": null, "params": [], "start": **********.418178, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/plugins/ecommerce/resources/views/orders/notification.blade.phpplugins/ecommerce::orders.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Forders%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::orders.notification"}, {"name": "1x __components::8394ebb1c2841e3a1166cd3fb0a6e03f", "param_count": null, "params": [], "start": **********.419364, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php__components::8394ebb1c2841e3a1166cd3fb0a6e03f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php&line=1", "ajax": false, "filename": "8394ebb1c2841e3a1166cd3fb0a6e03f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8394ebb1c2841e3a1166cd3fb0a6e03f"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.437789, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "2x __components::d059faaba602d6895d68258ab3c890a6", "param_count": null, "params": [], "start": **********.442332, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/d059faaba602d6895d68258ab3c890a6.blade.php__components::d059faaba602d6895d68258ab3c890a6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fd059faaba602d6895d68258ab3c890a6.blade.php&line=1", "ajax": false, "filename": "d059faaba602d6895d68258ab3c890a6.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d059faaba602d6895d68258ab3c890a6"}, {"name": "2x __components::a3cb4601eb64a80dc01a3c268590a3c8", "param_count": null, "params": [], "start": **********.443893, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/a3cb4601eb64a80dc01a3c268590a3c8.blade.php__components::a3cb4601eb64a80dc01a3c268590a3c8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fa3cb4601eb64a80dc01a3c268590a3c8.blade.php&line=1", "ajax": false, "filename": "a3cb4601eb64a80dc01a3c268590a3c8.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::a3cb4601eb64a80dc01a3c268590a3c8"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.44504, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.450138, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.450454, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "22x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.4683, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 22, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "80x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.469118, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 80, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::e3b17f7ce9738894b58a8b70b9624457", "param_count": null, "params": [], "start": **********.470425, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/e3b17f7ce9738894b58a8b70b9624457.blade.php__components::e3b17f7ce9738894b58a8b70b9624457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fe3b17f7ce9738894b58a8b70b9624457.blade.php&line=1", "ajax": false, "filename": "e3b17f7ce9738894b58a8b70b9624457.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3b17f7ce9738894b58a8b70b9624457"}, {"name": "1x __components::0c6e6838aa476b78aace81114936689c", "param_count": null, "params": [], "start": **********.473713, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/0c6e6838aa476b78aace81114936689c.blade.php__components::0c6e6838aa476b78aace81114936689c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F0c6e6838aa476b78aace81114936689c.blade.php&line=1", "ajax": false, "filename": "0c6e6838aa476b78aace81114936689c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0c6e6838aa476b78aace81114936689c"}, {"name": "11x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.474615, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "11x 8def1252668913628243c4d363bee1ef::navbar.badge-count", "param_count": null, "params": [], "start": **********.474981, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/navbar/badge-count.blade.php8def1252668913628243c4d363bee1ef::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 11, "name_original": "8def1252668913628243c4d363bee1ef::navbar.badge-count"}, {"name": "1x __components::37fae22c8e215ea2e54e69a5e3a007cc", "param_count": null, "params": [], "start": **********.476961, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/37fae22c8e215ea2e54e69a5e3a007cc.blade.php__components::37fae22c8e215ea2e54e69a5e3a007cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F37fae22c8e215ea2e54e69a5e3a007cc.blade.php&line=1", "ajax": false, "filename": "37fae22c8e215ea2e54e69a5e3a007cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::37fae22c8e215ea2e54e69a5e3a007cc"}, {"name": "1x __components::8916176d99d4ae2024cd36e11e35b821", "param_count": null, "params": [], "start": **********.478722, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/8916176d99d4ae2024cd36e11e35b821.blade.php__components::8916176d99d4ae2024cd36e11e35b821", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F8916176d99d4ae2024cd36e11e35b821.blade.php&line=1", "ajax": false, "filename": "8916176d99d4ae2024cd36e11e35b821.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8916176d99d4ae2024cd36e11e35b821"}, {"name": "1x __components::bd27433a6607127acdaf6dc541ab2435", "param_count": null, "params": [], "start": **********.481557, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/bd27433a6607127acdaf6dc541ab2435.blade.php__components::bd27433a6607127acdaf6dc541ab2435", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fbd27433a6607127acdaf6dc541ab2435.blade.php&line=1", "ajax": false, "filename": "bd27433a6607127acdaf6dc541ab2435.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd27433a6607127acdaf6dc541ab2435"}, {"name": "1x __components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "param_count": null, "params": [], "start": **********.485151, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php__components::5f4f9ebbae249bdc8b0d599a1ac6ad06", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php&line=1", "ajax": false, "filename": "5f4f9ebbae249bdc8b0d599a1ac6ad06.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5f4f9ebbae249bdc8b0d599a1ac6ad06"}, {"name": "1x __components::080b92e00b37bcc97c1cd249894494a2", "param_count": null, "params": [], "start": **********.489568, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/080b92e00b37bcc97c1cd249894494a2.blade.php__components::080b92e00b37bcc97c1cd249894494a2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F080b92e00b37bcc97c1cd249894494a2.blade.php&line=1", "ajax": false, "filename": "080b92e00b37bcc97c1cd249894494a2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::080b92e00b37bcc97c1cd249894494a2"}, {"name": "1x __components::6a26943e77184871de1629f41c534094", "param_count": null, "params": [], "start": **********.49263, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/6a26943e77184871de1629f41c534094.blade.php__components::6a26943e77184871de1629f41c534094", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F6a26943e77184871de1629f41c534094.blade.php&line=1", "ajax": false, "filename": "6a26943e77184871de1629f41c534094.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6a26943e77184871de1629f41c534094"}, {"name": "1x __components::8f29f8012139c7a3eb6593c906e1db38", "param_count": null, "params": [], "start": **********.495096, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/8f29f8012139c7a3eb6593c906e1db38.blade.php__components::8f29f8012139c7a3eb6593c906e1db38", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F8f29f8012139c7a3eb6593c906e1db38.blade.php&line=1", "ajax": false, "filename": "8f29f8012139c7a3eb6593c906e1db38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8f29f8012139c7a3eb6593c906e1db38"}, {"name": "1x __components::4553f5b37130b2effba490dbdf5419d2", "param_count": null, "params": [], "start": **********.499742, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/4553f5b37130b2effba490dbdf5419d2.blade.php__components::4553f5b37130b2effba490dbdf5419d2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F4553f5b37130b2effba490dbdf5419d2.blade.php&line=1", "ajax": false, "filename": "4553f5b37130b2effba490dbdf5419d2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4553f5b37130b2effba490dbdf5419d2"}, {"name": "1x __components::59c947fc9b2121a5885d4f4e7b1242d8", "param_count": null, "params": [], "start": **********.502824, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/59c947fc9b2121a5885d4f4e7b1242d8.blade.php__components::59c947fc9b2121a5885d4f4e7b1242d8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F59c947fc9b2121a5885d4f4e7b1242d8.blade.php&line=1", "ajax": false, "filename": "59c947fc9b2121a5885d4f4e7b1242d8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::59c947fc9b2121a5885d4f4e7b1242d8"}, {"name": "1x __components::76de3b53a2c2ccc2ffb093fedb19df44", "param_count": null, "params": [], "start": **********.504953, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/76de3b53a2c2ccc2ffb093fedb19df44.blade.php__components::76de3b53a2c2ccc2ffb093fedb19df44", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F76de3b53a2c2ccc2ffb093fedb19df44.blade.php&line=1", "ajax": false, "filename": "76de3b53a2c2ccc2ffb093fedb19df44.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::76de3b53a2c2ccc2ffb093fedb19df44"}, {"name": "1x __components::1e9698c460b468bfcaf0f7dbbebf9bf0", "param_count": null, "params": [], "start": **********.507654, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php__components::1e9698c460b468bfcaf0f7dbbebf9bf0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php&line=1", "ajax": false, "filename": "1e9698c460b468bfcaf0f7dbbebf9bf0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1e9698c460b468bfcaf0f7dbbebf9bf0"}, {"name": "2x __components::0d4eb4544a5328bea40b7b01743b8f82", "param_count": null, "params": [], "start": **********.509856, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/0d4eb4544a5328bea40b7b01743b8f82.blade.php__components::0d4eb4544a5328bea40b7b01743b8f82", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F0d4eb4544a5328bea40b7b01743b8f82.blade.php&line=1", "ajax": false, "filename": "0d4eb4544a5328bea40b7b01743b8f82.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0d4eb4544a5328bea40b7b01743b8f82"}, {"name": "2x __components::5270fef4db64e6c2fedf42ea8ac88f25", "param_count": null, "params": [], "start": **********.51236, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/5270fef4db64e6c2fedf42ea8ac88f25.blade.php__components::5270fef4db64e6c2fedf42ea8ac88f25", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F5270fef4db64e6c2fedf42ea8ac88f25.blade.php&line=1", "ajax": false, "filename": "5270fef4db64e6c2fedf42ea8ac88f25.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5270fef4db64e6c2fedf42ea8ac88f25"}, {"name": "1x __components::84f17fac377525e2e49f32058361220b", "param_count": null, "params": [], "start": **********.516668, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/84f17fac377525e2e49f32058361220b.blade.php__components::84f17fac377525e2e49f32058361220b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F84f17fac377525e2e49f32058361220b.blade.php&line=1", "ajax": false, "filename": "84f17fac377525e2e49f32058361220b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::84f17fac377525e2e49f32058361220b"}, {"name": "1x __components::1c8617dee734f51544a3883923ddca6f", "param_count": null, "params": [], "start": **********.52213, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/1c8617dee734f51544a3883923ddca6f.blade.php__components::1c8617dee734f51544a3883923ddca6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F1c8617dee734f51544a3883923ddca6f.blade.php&line=1", "ajax": false, "filename": "1c8617dee734f51544a3883923ddca6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1c8617dee734f51544a3883923ddca6f"}, {"name": "1x __components::3d3bfe5e8598abeb74083f6c26233cb5", "param_count": null, "params": [], "start": **********.524429, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/3d3bfe5e8598abeb74083f6c26233cb5.blade.php__components::3d3bfe5e8598abeb74083f6c26233cb5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F3d3bfe5e8598abeb74083f6c26233cb5.blade.php&line=1", "ajax": false, "filename": "3d3bfe5e8598abeb74083f6c26233cb5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3d3bfe5e8598abeb74083f6c26233cb5"}, {"name": "1x __components::35fe997a7b87ef55d749630606a50a1b", "param_count": null, "params": [], "start": **********.527372, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/35fe997a7b87ef55d749630606a50a1b.blade.php__components::35fe997a7b87ef55d749630606a50a1b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F35fe997a7b87ef55d749630606a50a1b.blade.php&line=1", "ajax": false, "filename": "35fe997a7b87ef55d749630606a50a1b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::35fe997a7b87ef55d749630606a50a1b"}, {"name": "1x __components::a4f1583597dec7e67a8ae044f0915dbe", "param_count": null, "params": [], "start": **********.532969, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/a4f1583597dec7e67a8ae044f0915dbe.blade.php__components::a4f1583597dec7e67a8ae044f0915dbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fa4f1583597dec7e67a8ae044f0915dbe.blade.php&line=1", "ajax": false, "filename": "a4f1583597dec7e67a8ae044f0915dbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a4f1583597dec7e67a8ae044f0915dbe"}, {"name": "1x __components::c0cbd16b0cc2226ec5536610974ba3c3", "param_count": null, "params": [], "start": **********.536056, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/c0cbd16b0cc2226ec5536610974ba3c3.blade.php__components::c0cbd16b0cc2226ec5536610974ba3c3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fc0cbd16b0cc2226ec5536610974ba3c3.blade.php&line=1", "ajax": false, "filename": "c0cbd16b0cc2226ec5536610974ba3c3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c0cbd16b0cc2226ec5536610974ba3c3"}, {"name": "2x __components::70b8df706e60982a72f15e9e2d486203", "param_count": null, "params": [], "start": **********.538935, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/70b8df706e60982a72f15e9e2d486203.blade.php__components::70b8df706e60982a72f15e9e2d486203", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F70b8df706e60982a72f15e9e2d486203.blade.php&line=1", "ajax": false, "filename": "70b8df706e60982a72f15e9e2d486203.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::70b8df706e60982a72f15e9e2d486203"}, {"name": "1x __components::dc78b90963e9d9963376e0e829411cea", "param_count": null, "params": [], "start": **********.542591, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/dc78b90963e9d9963376e0e829411cea.blade.php__components::dc78b90963e9d9963376e0e829411cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fdc78b90963e9d9963376e0e829411cea.blade.php&line=1", "ajax": false, "filename": "dc78b90963e9d9963376e0e829411cea.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc78b90963e9d9963376e0e829411cea"}, {"name": "4x __components::7a6e3d0dfcd673b5659893aa4dd54e33", "param_count": null, "params": [], "start": **********.545407, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/7a6e3d0dfcd673b5659893aa4dd54e33.blade.php__components::7a6e3d0dfcd673b5659893aa4dd54e33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F7a6e3d0dfcd673b5659893aa4dd54e33.blade.php&line=1", "ajax": false, "filename": "7a6e3d0dfcd673b5659893aa4dd54e33.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::7a6e3d0dfcd673b5659893aa4dd54e33"}, {"name": "1x __components::481a833ebeb573258c941c925aa45f7b", "param_count": null, "params": [], "start": **********.549771, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/481a833ebeb573258c941c925aa45f7b.blade.php__components::481a833ebeb573258c941c925aa45f7b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F481a833ebeb573258c941c925aa45f7b.blade.php&line=1", "ajax": false, "filename": "481a833ebeb573258c941c925aa45f7b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::481a833ebeb573258c941c925aa45f7b"}, {"name": "1x __components::b42bb0aa5fceca31ad61711414a614f0", "param_count": null, "params": [], "start": **********.554098, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/b42bb0aa5fceca31ad61711414a614f0.blade.php__components::b42bb0aa5fceca31ad61711414a614f0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fb42bb0aa5fceca31ad61711414a614f0.blade.php&line=1", "ajax": false, "filename": "b42bb0aa5fceca31ad61711414a614f0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b42bb0aa5fceca31ad61711414a614f0"}, {"name": "3x __components::1c969038219bd5c599f1ca2d81401cea", "param_count": null, "params": [], "start": **********.560469, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/1c969038219bd5c599f1ca2d81401cea.blade.php__components::1c969038219bd5c599f1ca2d81401cea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F1c969038219bd5c599f1ca2d81401cea.blade.php&line=1", "ajax": false, "filename": "1c969038219bd5c599f1ca2d81401cea.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::1c969038219bd5c599f1ca2d81401cea"}, {"name": "1x __components::471e83668278198d730a7a3f4a475d45", "param_count": null, "params": [], "start": **********.564969, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/471e83668278198d730a7a3f4a475d45.blade.php__components::471e83668278198d730a7a3f4a475d45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F471e83668278198d730a7a3f4a475d45.blade.php&line=1", "ajax": false, "filename": "471e83668278198d730a7a3f4a475d45.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::471e83668278198d730a7a3f4a475d45"}, {"name": "1x __components::d7b40194b2b4ba91a975fc9aafe2d3e8", "param_count": null, "params": [], "start": **********.569539, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php__components::d7b40194b2b4ba91a975fc9aafe2d3e8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fd7b40194b2b4ba91a975fc9aafe2d3e8.blade.php&line=1", "ajax": false, "filename": "d7b40194b2b4ba91a975fc9aafe2d3e8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7b40194b2b4ba91a975fc9aafe2d3e8"}, {"name": "1x __components::acb69140835a74210411469faeab3034", "param_count": null, "params": [], "start": **********.574579, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/acb69140835a74210411469faeab3034.blade.php__components::acb69140835a74210411469faeab3034", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Facb69140835a74210411469faeab3034.blade.php&line=1", "ajax": false, "filename": "acb69140835a74210411469faeab3034.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acb69140835a74210411469faeab3034"}, {"name": "1x __components::19cd49cd69455e40dc223df6b4eaf954", "param_count": null, "params": [], "start": **********.577932, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/19cd49cd69455e40dc223df6b4eaf954.blade.php__components::19cd49cd69455e40dc223df6b4eaf954", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F19cd49cd69455e40dc223df6b4eaf954.blade.php&line=1", "ajax": false, "filename": "19cd49cd69455e40dc223df6b4eaf954.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::19cd49cd69455e40dc223df6b4eaf954"}, {"name": "1x __components::2979b72aeeca0047ecdecc3ad66e7e16", "param_count": null, "params": [], "start": **********.581066, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/2979b72aeeca0047ecdecc3ad66e7e16.blade.php__components::2979b72aeeca0047ecdecc3ad66e7e16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F2979b72aeeca0047ecdecc3ad66e7e16.blade.php&line=1", "ajax": false, "filename": "2979b72aeeca0047ecdecc3ad66e7e16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2979b72aeeca0047ecdecc3ad66e7e16"}, {"name": "1x __components::3c40febd70fcdc245d99ae7cd02cface", "param_count": null, "params": [], "start": **********.584898, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/3c40febd70fcdc245d99ae7cd02cface.blade.php__components::3c40febd70fcdc245d99ae7cd02cface", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F3c40febd70fcdc245d99ae7cd02cface.blade.php&line=1", "ajax": false, "filename": "3c40febd70fcdc245d99ae7cd02cface.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3c40febd70fcdc245d99ae7cd02cface"}, {"name": "2x __components::90ccac5c8bbb25741ef262bfd81c7551", "param_count": null, "params": [], "start": **********.587065, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/90ccac5c8bbb25741ef262bfd81c7551.blade.php__components::90ccac5c8bbb25741ef262bfd81c7551", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F90ccac5c8bbb25741ef262bfd81c7551.blade.php&line=1", "ajax": false, "filename": "90ccac5c8bbb25741ef262bfd81c7551.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::90ccac5c8bbb25741ef262bfd81c7551"}, {"name": "1x __components::d7ced212b797c29086a7922a858f3070", "param_count": null, "params": [], "start": **********.593593, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/d7ced212b797c29086a7922a858f3070.blade.php__components::d7ced212b797c29086a7922a858f3070", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fd7ced212b797c29086a7922a858f3070.blade.php&line=1", "ajax": false, "filename": "d7ced212b797c29086a7922a858f3070.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d7ced212b797c29086a7922a858f3070"}, {"name": "2x __components::9d41a7757b46012fb4a0d6634d04a1e0", "param_count": null, "params": [], "start": **********.596287, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/9d41a7757b46012fb4a0d6634d04a1e0.blade.php__components::9d41a7757b46012fb4a0d6634d04a1e0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F9d41a7757b46012fb4a0d6634d04a1e0.blade.php&line=1", "ajax": false, "filename": "9d41a7757b46012fb4a0d6634d04a1e0.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9d41a7757b46012fb4a0d6634d04a1e0"}, {"name": "3x __components::86b7e33bd2198279086ebb1f21c0e2cc", "param_count": null, "params": [], "start": **********.601091, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/86b7e33bd2198279086ebb1f21c0e2cc.blade.php__components::86b7e33bd2198279086ebb1f21c0e2cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F86b7e33bd2198279086ebb1f21c0e2cc.blade.php&line=1", "ajax": false, "filename": "86b7e33bd2198279086ebb1f21c0e2cc.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::86b7e33bd2198279086ebb1f21c0e2cc"}, {"name": "1x __components::8c52d9b1ef0685ec10fdc3e877751e02", "param_count": null, "params": [], "start": **********.607004, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/8c52d9b1ef0685ec10fdc3e877751e02.blade.php__components::8c52d9b1ef0685ec10fdc3e877751e02", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F8c52d9b1ef0685ec10fdc3e877751e02.blade.php&line=1", "ajax": false, "filename": "8c52d9b1ef0685ec10fdc3e877751e02.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8c52d9b1ef0685ec10fdc3e877751e02"}, {"name": "1x __components::42668be6e8e5266862c6994eaa88bb55", "param_count": null, "params": [], "start": **********.61114, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/42668be6e8e5266862c6994eaa88bb55.blade.php__components::42668be6e8e5266862c6994eaa88bb55", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F42668be6e8e5266862c6994eaa88bb55.blade.php&line=1", "ajax": false, "filename": "42668be6e8e5266862c6994eaa88bb55.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42668be6e8e5266862c6994eaa88bb55"}, {"name": "1x __components::311d8c591d63d3cbd12dceb2fb1ac1c1", "param_count": null, "params": [], "start": **********.615572, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php__components::311d8c591d63d3cbd12dceb2fb1ac1c1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php&line=1", "ajax": false, "filename": "311d8c591d63d3cbd12dceb2fb1ac1c1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::311d8c591d63d3cbd12dceb2fb1ac1c1"}, {"name": "1x __components::613233f0072612a02c74dd1699c0b74c", "param_count": null, "params": [], "start": **********.625433, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/613233f0072612a02c74dd1699c0b74c.blade.php__components::613233f0072612a02c74dd1699c0b74c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F613233f0072612a02c74dd1699c0b74c.blade.php&line=1", "ajax": false, "filename": "613233f0072612a02c74dd1699c0b74c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::613233f0072612a02c74dd1699c0b74c"}, {"name": "1x __components::16225ede2ef5cc17292fd2eb9026fc80", "param_count": null, "params": [], "start": **********.627694, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/16225ede2ef5cc17292fd2eb9026fc80.blade.php__components::16225ede2ef5cc17292fd2eb9026fc80", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F16225ede2ef5cc17292fd2eb9026fc80.blade.php&line=1", "ajax": false, "filename": "16225ede2ef5cc17292fd2eb9026fc80.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16225ede2ef5cc17292fd2eb9026fc80"}, {"name": "1x __components::46010cb1cb88bb5ead5d94603a4a3d16", "param_count": null, "params": [], "start": **********.63073, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/46010cb1cb88bb5ead5d94603a4a3d16.blade.php__components::46010cb1cb88bb5ead5d94603a4a3d16", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F46010cb1cb88bb5ead5d94603a4a3d16.blade.php&line=1", "ajax": false, "filename": "46010cb1cb88bb5ead5d94603a4a3d16.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::46010cb1cb88bb5ead5d94603a4a3d16"}, {"name": "1x __components::34e3d89351b1208b7f313125eec52879", "param_count": null, "params": [], "start": **********.634722, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/34e3d89351b1208b7f313125eec52879.blade.php__components::34e3d89351b1208b7f313125eec52879", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F34e3d89351b1208b7f313125eec52879.blade.php&line=1", "ajax": false, "filename": "34e3d89351b1208b7f313125eec52879.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::34e3d89351b1208b7f313125eec52879"}, {"name": "1x __components::67034900569133b2c83b32da3dd4f5e5", "param_count": null, "params": [], "start": **********.637027, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/67034900569133b2c83b32da3dd4f5e5.blade.php__components::67034900569133b2c83b32da3dd4f5e5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F67034900569133b2c83b32da3dd4f5e5.blade.php&line=1", "ajax": false, "filename": "67034900569133b2c83b32da3dd4f5e5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::67034900569133b2c83b32da3dd4f5e5"}, {"name": "1x __components::0d623715926c24f9fbc8a4b72c106d5d", "param_count": null, "params": [], "start": **********.647096, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/0d623715926c24f9fbc8a4b72c106d5d.blade.php__components::0d623715926c24f9fbc8a4b72c106d5d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F0d623715926c24f9fbc8a4b72c106d5d.blade.php&line=1", "ajax": false, "filename": "0d623715926c24f9fbc8a4b72c106d5d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0d623715926c24f9fbc8a4b72c106d5d"}, {"name": "1x __components::6c50fc55276d93f8ed03f5c85273b6cc", "param_count": null, "params": [], "start": **********.652298, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/6c50fc55276d93f8ed03f5c85273b6cc.blade.php__components::6c50fc55276d93f8ed03f5c85273b6cc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F6c50fc55276d93f8ed03f5c85273b6cc.blade.php&line=1", "ajax": false, "filename": "6c50fc55276d93f8ed03f5c85273b6cc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6c50fc55276d93f8ed03f5c85273b6cc"}, {"name": "1x __components::084d2b43c9ab4b881d9b34a15580aa2d", "param_count": null, "params": [], "start": **********.655221, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/084d2b43c9ab4b881d9b34a15580aa2d.blade.php__components::084d2b43c9ab4b881d9b34a15580aa2d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F084d2b43c9ab4b881d9b34a15580aa2d.blade.php&line=1", "ajax": false, "filename": "084d2b43c9ab4b881d9b34a15580aa2d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::084d2b43c9ab4b881d9b34a15580aa2d"}, {"name": "2x __components::b33d20952e90e5c4a596ff58ad010448", "param_count": null, "params": [], "start": **********.657933, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/b33d20952e90e5c4a596ff58ad010448.blade.php__components::b33d20952e90e5c4a596ff58ad010448", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fb33d20952e90e5c4a596ff58ad010448.blade.php&line=1", "ajax": false, "filename": "b33d20952e90e5c4a596ff58ad010448.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::b33d20952e90e5c4a596ff58ad010448"}, {"name": "1x __components::7db8cad89359963c1e9aa8fcc6c89817", "param_count": null, "params": [], "start": **********.66044, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/7db8cad89359963c1e9aa8fcc6c89817.blade.php__components::7db8cad89359963c1e9aa8fcc6c89817", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F7db8cad89359963c1e9aa8fcc6c89817.blade.php&line=1", "ajax": false, "filename": "7db8cad89359963c1e9aa8fcc6c89817.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7db8cad89359963c1e9aa8fcc6c89817"}, {"name": "1x __components::325be2a8c3ca3843efa76c03adaee1dc", "param_count": null, "params": [], "start": **********.667863, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/325be2a8c3ca3843efa76c03adaee1dc.blade.php__components::325be2a8c3ca3843efa76c03adaee1dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F325be2a8c3ca3843efa76c03adaee1dc.blade.php&line=1", "ajax": false, "filename": "325be2a8c3ca3843efa76c03adaee1dc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::325be2a8c3ca3843efa76c03adaee1dc"}, {"name": "1x __components::e12a669ffa0346a27198bed32e63b7ba", "param_count": null, "params": [], "start": **********.670325, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/e12a669ffa0346a27198bed32e63b7ba.blade.php__components::e12a669ffa0346a27198bed32e63b7ba", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fe12a669ffa0346a27198bed32e63b7ba.blade.php&line=1", "ajax": false, "filename": "e12a669ffa0346a27198bed32e63b7ba.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e12a669ffa0346a27198bed32e63b7ba"}, {"name": "1x __components::9d0b20db301db9a47503a93a879bb206", "param_count": null, "params": [], "start": **********.67228, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/9d0b20db301db9a47503a93a879bb206.blade.php__components::9d0b20db301db9a47503a93a879bb206", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F9d0b20db301db9a47503a93a879bb206.blade.php&line=1", "ajax": false, "filename": "9d0b20db301db9a47503a93a879bb206.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9d0b20db301db9a47503a93a879bb206"}, {"name": "1x __components::b985af7bcdacbeac70eaf3979ad19f5a", "param_count": null, "params": [], "start": **********.674365, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/b985af7bcdacbeac70eaf3979ad19f5a.blade.php__components::b985af7bcdacbeac70eaf3979ad19f5a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fb985af7bcdacbeac70eaf3979ad19f5a.blade.php&line=1", "ajax": false, "filename": "b985af7bcdacbeac70eaf3979ad19f5a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b985af7bcdacbeac70eaf3979ad19f5a"}, {"name": "1x __components::a0bb1d43b71cff86abe626fd376492e9", "param_count": null, "params": [], "start": **********.676536, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/a0bb1d43b71cff86abe626fd376492e9.blade.php__components::a0bb1d43b71cff86abe626fd376492e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fa0bb1d43b71cff86abe626fd376492e9.blade.php&line=1", "ajax": false, "filename": "a0bb1d43b71cff86abe626fd376492e9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a0bb1d43b71cff86abe626fd376492e9"}, {"name": "1x __components::e25f2b305e6de46c04f91fa1ce50f68f", "param_count": null, "params": [], "start": **********.678744, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/e25f2b305e6de46c04f91fa1ce50f68f.blade.php__components::e25f2b305e6de46c04f91fa1ce50f68f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fe25f2b305e6de46c04f91fa1ce50f68f.blade.php&line=1", "ajax": false, "filename": "e25f2b305e6de46c04f91fa1ce50f68f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e25f2b305e6de46c04f91fa1ce50f68f"}, {"name": "1x __components::69152f707ea1358f8997b77a28e38a6f", "param_count": null, "params": [], "start": **********.680839, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/69152f707ea1358f8997b77a28e38a6f.blade.php__components::69152f707ea1358f8997b77a28e38a6f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F69152f707ea1358f8997b77a28e38a6f.blade.php&line=1", "ajax": false, "filename": "69152f707ea1358f8997b77a28e38a6f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69152f707ea1358f8997b77a28e38a6f"}, {"name": "1x __components::b85eba35d3b7929c2988678b725baebf", "param_count": null, "params": [], "start": **********.684693, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/b85eba35d3b7929c2988678b725baebf.blade.php__components::b85eba35d3b7929c2988678b725baebf", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fb85eba35d3b7929c2988678b725baebf.blade.php&line=1", "ajax": false, "filename": "b85eba35d3b7929c2988678b725baebf.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b85eba35d3b7929c2988678b725baebf"}, {"name": "1x __components::1f3d3b83c612f68036b4d79d53ae851e", "param_count": null, "params": [], "start": **********.687637, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/1f3d3b83c612f68036b4d79d53ae851e.blade.php__components::1f3d3b83c612f68036b4d79d53ae851e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F1f3d3b83c612f68036b4d79d53ae851e.blade.php&line=1", "ajax": false, "filename": "1f3d3b83c612f68036b4d79d53ae851e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1f3d3b83c612f68036b4d79d53ae851e"}, {"name": "1x __components::f143d1296cea16d82e2c87956e445593", "param_count": null, "params": [], "start": **********.691228, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/f143d1296cea16d82e2c87956e445593.blade.php__components::f143d1296cea16d82e2c87956e445593", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Ff143d1296cea16d82e2c87956e445593.blade.php&line=1", "ajax": false, "filename": "f143d1296cea16d82e2c87956e445593.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f143d1296cea16d82e2c87956e445593"}, {"name": "1x __components::8cae4e5056b67c6778a54389a62ac7a0", "param_count": null, "params": [], "start": **********.693407, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/8cae4e5056b67c6778a54389a62ac7a0.blade.php__components::8cae4e5056b67c6778a54389a62ac7a0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F8cae4e5056b67c6778a54389a62ac7a0.blade.php&line=1", "ajax": false, "filename": "8cae4e5056b67c6778a54389a62ac7a0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8cae4e5056b67c6778a54389a62ac7a0"}, {"name": "1x __components::d5e509b6eb9084ec382ec05ccab41d1a", "param_count": null, "params": [], "start": **********.695485, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/d5e509b6eb9084ec382ec05ccab41d1a.blade.php__components::d5e509b6eb9084ec382ec05ccab41d1a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fd5e509b6eb9084ec382ec05ccab41d1a.blade.php&line=1", "ajax": false, "filename": "d5e509b6eb9084ec382ec05ccab41d1a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d5e509b6eb9084ec382ec05ccab41d1a"}, {"name": "1x __components::e81a46563ed9378aa4d9a4fcb55e743e", "param_count": null, "params": [], "start": **********.699044, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/e81a46563ed9378aa4d9a4fcb55e743e.blade.php__components::e81a46563ed9378aa4d9a4fcb55e743e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fe81a46563ed9378aa4d9a4fcb55e743e.blade.php&line=1", "ajax": false, "filename": "e81a46563ed9378aa4d9a4fcb55e743e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e81a46563ed9378aa4d9a4fcb55e743e"}, {"name": "1x __components::da3c3de008e5793cbbdad005d78f49b1", "param_count": null, "params": [], "start": **********.706495, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/da3c3de008e5793cbbdad005d78f49b1.blade.php__components::da3c3de008e5793cbbdad005d78f49b1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2Fda3c3de008e5793cbbdad005d78f49b1.blade.php&line=1", "ajax": false, "filename": "da3c3de008e5793cbbdad005d78f49b1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::da3c3de008e5793cbbdad005d78f49b1"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.70778, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.708183, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.709244, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.709598, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.710484, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::system.license-invalid", "param_count": null, "params": [], "start": **********.711058, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/system/license-invalid.blade.phpcore/base::system.license-invalid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Flicense-invalid.blade.php&line=1", "ajax": false, "filename": "license-invalid.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.license-invalid"}, {"name": "2x 8def1252668913628243c4d363bee1ef::alert", "param_count": null, "params": [], "start": **********.711708, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/alert.blade.php8def1252668913628243c4d363bee1ef::alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::alert"}, {"name": "1x core/base::system.partials.license-activation-modal", "param_count": null, "params": [], "start": **********.712237, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/system/partials/license-activation-modal.blade.phpcore/base::system.partials.license-activation-modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fsystem%2Fpartials%2Flicense-activation-modal.blade.php&line=1", "ajax": false, "filename": "license-activation-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::system.partials.license-activation-modal"}, {"name": "1x 8def1252668913628243c4d363bee1ef::license.form", "param_count": null, "params": [], "start": **********.712758, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/license/form.blade.php8def1252668913628243c4d363bee1ef::license.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flicense%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::license.form"}, {"name": "1x __components::27ec08f706fece52ef1cc0ec5563cef9", "param_count": null, "params": [], "start": **********.715199, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/27ec08f706fece52ef1cc0ec5563cef9.blade.php__components::27ec08f706fece52ef1cc0ec5563cef9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F27ec08f706fece52ef1cc0ec5563cef9.blade.php&line=1", "ajax": false, "filename": "27ec08f706fece52ef1cc0ec5563cef9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27ec08f706fece52ef1cc0ec5563cef9"}, {"name": "2x 8def1252668913628243c4d363bee1ef::form.helper-text", "param_count": null, "params": [], "start": **********.718725, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/helper-text.blade.php8def1252668913628243c4d363bee1ef::form.helper-text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fhelper-text.blade.php&line=1", "ajax": false, "filename": "helper-text.blade.php", "line": "?"}, "render_count": 2, "name_original": "8def1252668913628243c4d363bee1ef::form.helper-text"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "param_count": null, "params": [], "start": **********.721867, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/on-off/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.on-off.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fon-off%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.on-off.checkbox"}, {"name": "1x core/base::components.form.checkbox", "param_count": null, "params": [], "start": **********.722259, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/checkbox.blade.phpcore/base::components.form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.form.checkbox"}, {"name": "1x ********************************::form-group", "param_count": null, "params": [], "start": **********.723809, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/setting/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fsetting%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form-group"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.725834, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::53362b6227831afe8e4d7d3436ab607f", "param_count": null, "params": [], "start": **********.729593, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/53362b6227831afe8e4d7d3436ab607f.blade.php__components::53362b6227831afe8e4d7d3436ab607f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F53362b6227831afe8e4d7d3436ab607f.blade.php&line=1", "ajax": false, "filename": "53362b6227831afe8e4d7d3436ab607f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::53362b6227831afe8e4d7d3436ab607f"}, {"name": "1x __components::9e76aef074ac8ea84c711b8437720a22", "param_count": null, "params": [], "start": **********.730625, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/9e76aef074ac8ea84c711b8437720a22.blade.php__components::9e76aef074ac8ea84c711b8437720a22", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F9e76aef074ac8ea84c711b8437720a22.blade.php&line=1", "ajax": false, "filename": "9e76aef074ac8ea84c711b8437720a22.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9e76aef074ac8ea84c711b8437720a22"}, {"name": "1x __components::04edbddbda254d131a3439b11c880f12", "param_count": null, "params": [], "start": **********.731748, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/04edbddbda254d131a3439b11c880f12.blade.php__components::04edbddbda254d131a3439b11c880f12", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F04edbddbda254d131a3439b11c880f12.blade.php&line=1", "ajax": false, "filename": "04edbddbda254d131a3439b11c880f12.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::04edbddbda254d131a3439b11c880f12"}, {"name": "1x 8def1252668913628243c4d363bee1ef::custom-template", "param_count": null, "params": [], "start": **********.733233, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/custom-template.blade.php8def1252668913628243c4d363bee1ef::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.733639, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "1x 8def1252668913628243c4d363bee1ef::loading", "param_count": null, "params": [], "start": **********.734897, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/loading.blade.php8def1252668913628243c4d363bee1ef::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::loading"}, {"name": "1x 8def1252668913628243c4d363bee1ef::form.checkbox", "param_count": null, "params": [], "start": **********.737301, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/form/checkbox.blade.php8def1252668913628243c4d363bee1ef::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::form.checkbox"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.741186, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x 8def1252668913628243c4d363bee1ef::debug-badge", "param_count": null, "params": [], "start": **********.952869, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/debug-badge.blade.php8def1252668913628243c4d363bee1ef::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::debug-badge"}, {"name": "1x __components::93ebde3601860db875cfe9a96164bda6", "param_count": null, "params": [], "start": **********.955946, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/93ebde3601860db875cfe9a96164bda6.blade.php__components::93ebde3601860db875cfe9a96164bda6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F93ebde3601860db875cfe9a96164bda6.blade.php&line=1", "ajax": false, "filename": "93ebde3601860db875cfe9a96164bda6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::93ebde3601860db875cfe9a96164bda6"}, {"name": "1x __components::16c15d36d71c18d83a7e9e7e1b68a92b", "param_count": null, "params": [], "start": **********.959048, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\storage\\framework\\views/16c15d36d71c18d83a7e9e7e1b68a92b.blade.php__components::16c15d36d71c18d83a7e9e7e1b68a92b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fstorage%2Fframework%2Fviews%2F16c15d36d71c18d83a7e9e7e1b68a92b.blade.php&line=1", "ajax": false, "filename": "16c15d36d71c18d83a7e9e7e1b68a92b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::16c15d36d71c18d83a7e9e7e1b68a92b"}, {"name": "1x 8def1252668913628243c4d363bee1ef::layouts.base", "param_count": null, "params": [], "start": **********.960351, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/layouts/base.blade.php8def1252668913628243c4d363bee1ef::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "8def1252668913628243c4d363bee1ef::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.961072, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.963547, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.966369, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.967927, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.968897, "type": "blade", "hash": "bladeD:\\laragon\\www\\adawliahshop\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00503, "accumulated_duration_str": "5.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.2518332, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 0, "width_percent": 8.946}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.256021, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "adawliahshop", "explain": null, "start_percent": 8.946, "width_percent": 6.561}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.26127, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 15.507, "width_percent": 6.163}, {"sql": "select * from `branches` where `status` = 'published' order by `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/BranchInventoryController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\BranchInventoryController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.2883492, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 21.67, "width_percent": 8.946}, {"sql": "select * from `ec_products` where `status` = 'published' order by `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/BranchInventoryController.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\BranchInventoryController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.2910209, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 30.616, "width_percent": 21.471}, {"sql": "select count(*) as aggregate from `ec_orders` where (`status` = 'pending' and `is_finished` = 1)", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.402536, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1401", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1401", "ajax": false, "filename": "HookServiceProvider.php", "line": "1401"}, "connection": "adawliahshop", "explain": null, "start_percent": 52.087, "width_percent": 8.151}, {"sql": "select * from `ec_orders` where (`status` = 'pending' and `is_finished` = 1) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.403651, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 60.239, "width_percent": 13.121}, {"sql": "select * from `ec_order_addresses` where `type` = 'shipping_address' and `ec_order_addresses`.`order_id` in (85, 86, 88, 89, 90, 91, 94, 95, 97, 98)", "type": "query", "params": [], "bindings": ["shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.410719, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 73.36, "width_percent": 8.35}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1401}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4150531, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 81.71, "width_percent": 9.94}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1445}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 76}], "start": **********.529501, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1445", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1445}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1445", "ajax": false, "filename": "HookServiceProvider.php", "line": "1445"}, "connection": "adawliahshop", "explain": null, "start_percent": 91.65, "width_percent": 8.35}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"retrieved": 85, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\BranchManagement\\Models\\Branch": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fbranch-management%2Fsrc%2FModels%2FBranch.php&line=1", "ajax": false, "filename": "Branch.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 109, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 109}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://adawliahshop.gc/admin/ecommerce/branch-inventory", "action_name": "ecommerce.branch-inventory.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\BranchInventoryController@index", "uri": "GET admin/ecommerce/branch-inventory", "controller": "Botble\\Ecommerce\\Http\\Controllers\\BranchInventoryController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FBranchInventoryController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/branch-inventory", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FBranchInventoryController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/BranchInventoryController.php:21-34</a>", "middleware": "web, core, auth", "duration": "1.68s", "peak_memory": "74MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-270485608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-270485608\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1291310289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1291310289\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1173789454 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">adawliahshop.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">https://adawliahshop.gc/admin/ecommerce/branch-inventory</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2673 characters\">botble_footprints_cookie=eyJpdiI6InBYMHJNeEtkbHFlbWpBZU5QVnQrYlE9PSIsInZhbHVlIjoiM0JxSk5CTXlleEZZMzNCb3h5cUdTVXBBajFKTVI0VFZxemc3eWtvNWdoZHF2bUdRbkZUM2hscVkvcWk1Q2RJRVRTVDBGNmZjVEpZNFozWFU4K0RsL09Fby9BZGx3NGRHdjdwbDFZWDg4YmJZUkpmSmJXa0EwSlRJUWhmTEdqY3UiLCJtYWMiOiI3ZDRiNDUwNGFkYzNlOWZhYTNkNTNkOTlkYTMwYTkxY2U0MTAxMzJlZDFlMzNjOGJjZTZjYWFlMGI2ZGRmMzNiIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IjBNcGkyazd4dExDbnlaSmNmUnpOd0E9PSIsInZhbHVlIjoiMUtFOGhLckFUdnpGWVRyekl0MVpCVTZSKytUYitPNk5LR3oxTUdTbDdHZUt5MmtWS1ZJV2d0NFNNY1FDeWhnenJ4VDhRQk9BMDRqNklkZXU5UlNHZ1dBdStUUThWZHl2dkJzUDh2V09HMDRwa0NCY0NsTmt3NGV4MVlBWmhmRDUvbnpKOGZXcnU4aHJMWjZqVnB3akdLWGxsWmlHNVpSSVFhUFIvbTM2UE5IY01IVDhUMTlLN3lST1RoWW1iYVFQeTdpdjBsYjZrUTl2eE5oSnJLMU8rZUFqRjVDaVZUYTRBamxBM3phMUd2SlZTOGVyNXh0NzNxeVdXdmU0N0pLZGU0OVJvSUU4YXg4ZmM3dDVsMkRwSkx4RjN1NWN4aW1QbS9QSGJLdjlUdktvRW1UR1BIT3hqYUZSRy84YnZGQmxRT3VpZ2JsSGlGZDM2MmF3KzdzMms2VEJ0UVhZSE0vTzZKWU5pQ3JKYUhhbXhKc0dxK056dWZHTkp3QlZ6bW1LRFpxcmlkeGpXMHpoNG5uS0hkSitQWlhPTnJWb1Z4TDZXcDJFNmNFVTZjcW5DNmMwQUd3QWhoUVMySGpIZUkrRWxlamNVOC9KYzl1ZlN6aWUrSTdHWlZGYVorZGkwVFo4Nys1UG5IcDdFcjBYSmE3anRRQXhGcWdZNzlQQWpCVTFpZDh3RHJMaG1EZHRkVXpoUFVUNlhnPT0iLCJtYWMiOiIxZTdkY2JiZTI5NWNmMjdkZGQ5YThhYjdlYmUwMzk4NWZjZDkzYzJiNmYwZDFhMmE5ZDA3NDdiZmQzY2E0MjBhIiwidGFnIjoiIn0%3D; perf_dv6Tr4n=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkpIaFpaMEp5SFBNQ0pmZisvZStlaFE9PSIsInZhbHVlIjoiVXJHbWNMUmptbk5paVZvK0ZHUHNDaDFVQWp3L1NOMEJVdWJnWVFKWHhHWWJ5ZlF5TkpZRE1sWGRBRk85bUtUek9xVUc0cHV3cEkzYUVLbzgzQTVvUUJwV2NBaGQ0R3ROUG9xN3hodUxicG9XUS9OZlE1dVVIRXBQaDg4REF4NEpHWjhrZ0Z4Mk9iV293d1JDd0p0YXR4ejNDZTdCNEc3SGRSUWpUQ3JLUmtlcUx5S250TWI3ajY5SkFqRFgvOFMxY3RRVjFQdEtKR3ppVGRnaVg2dVpKUXdWZ0NzVndkdDVqZU9la2FGdmFDcz0iLCJtYWMiOiJmMjllOGFmY2RjOGE0OTQ2ZjdjMjdlMDVmMjFhZWY5ZDA2NTcxNTkzNDA4NmU1OGNlZWNlZjJkM2VkOGE4M2U0IiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; cookie_for_consent={&quot;essential&quot;:true}; XSRF-TOKEN=eyJpdiI6Ik9tU0twUk1tR29Jay9Rdms0QXlhdlE9PSIsInZhbHVlIjoidFlHSTNkTUF0ZEhWUVJJSEhGRHhyaGNmYWN3aUtYOGRMalhoTFlib2JSNkVGUG5yN2NNejV6b09aS3k5SG9UNkNCZjFLazY2MEYrN2RaU2pBdkxTUDJ6WkErcjBmZUFlOWJ1d1VlOS9iaTdDY0ZWbnp2UUtxT1Baa0ZPaTVzWi8iLCJtYWMiOiI5NGMyZGQ1MDE1NjNhYmZlNTVmN2FhZmE4MGFmZDI0ZGY1YjcwZGYzN2YwNjM1NjcyODc2MjgzODY5OTlhNjkxIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlNSQXlrUWo2OGZiRHQvNmxLbUphMEE9PSIsInZhbHVlIjoiQ2hYeThEd29PQytqQXVKUmlYTHl2KzhvRzZXV0tvMWlYZ0ZsR21OUUN2ckZ6Z0pRWXFHRjZwQXVYcmN1Uno1QU5ra0I1MFUrNDhYZ2ZzRFRHb3hodUZ2OERmb2xOTXFMMVZTcjJYTmt1M0pLQWpzcVVRSndSTURvSmxUUjUvTU8iLCJtYWMiOiI0MTFhNjMxN2YxMjhiYmZmYTMzN2EyYzM0ZTA5Mjc4NTc5NjAwZDQ2OTZhOTBmMGRkZDhjZDliODQ4YzY5ODY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173789454\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2091657534 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b611e49ca9c9e01d12f55b9fdbec097ba769ddeb</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;footprint&quot;:&quot;b611e49ca9c9e01d12f55b9fdbec097ba769ddeb&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;adawliahshop.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str title=\"18 characters\">{&quot;essential&quot;:true}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOi7kX3uphlBCTLAiO3znuue74DN3GeKZVAdyGvz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091657534\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-879553050 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 01 Sep 2025 17:09:09 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879553050\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1682773919 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>5</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>+</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://adawliahshop.gc/admin/ecommerce/branch-inventory</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>87</span> => <span class=sf-dump-num>1756743776</span>\n    <span class=sf-dump-key>11</span> => <span class=sf-dump-num>1756743874</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>recently_viewed_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4387</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011230000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:44:35.077642 from now\nDST Off\">2025-09-01 16:24:34.900653 UTC (+00:00)</span>\n    </samp>}\n    \"<span class=sf-dump-key>recently_viewed</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4388</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>10bfb15bf3664801511d8fd701d75ab4</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4389</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">10bfb15bf3664801511d8fd701d75ab4</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>87</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">Camera</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>150.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4390</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: \"<span class=sf-dump-str title=\"31 characters\">Botble\\Ecommerce\\Models\\Product</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743777\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743777</span></span> {<a class=sf-dump-ref>#4391</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011270000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:46:12.792849 from now\nDST Off\">2025-09-01 16:22:57.185962 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743777\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743777</span></span> {<a class=sf-dump-ref>#4392</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000011280000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:46:12.792911 from now\nDST Off\">2025-09-01 16:22:57.185954 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n        \"<span class=sf-dump-key>620d670d95f0419e35f9182695918c68</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItem</span></span> {<a class=sf-dump-ref>#4393</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">rowId</span>: \"<span class=sf-dump-str title=\"32 characters\">620d670d95f0419e35f9182695918c68</span>\"\n          +<span class=sf-dump-public title=\"Public property\">id</span>: <span class=sf-dump-num>11</span>\n          +<span class=sf-dump-public title=\"Public property\">qty</span>: <span class=sf-dump-num>1</span>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"40 characters\">Xbox One Wireless Controller Black Color</span>\"\n          +<span class=sf-dump-public title=\"Public property\">price</span>: <span class=sf-dump-num>1130.0</span>\n          +<span class=sf-dump-public title=\"Public property\">options</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Cart\\CartItemOptions\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Cart</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CartItemOptions</span></span> {<a class=sf-dump-ref>#4394</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">associatedModel</span>: \"<span class=sf-dump-str title=\"31 characters\">Botble\\Ecommerce\\Models\\Product</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">taxRate</span>: <span class=sf-dump-num>0.0</span>\n          +<span class=sf-dump-public title=\"Public property\">updated_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4395</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000112b0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:44:35.078298 from now\nDST Off\">2025-09-01 16:24:34.900621 UTC (+00:00)</span>\n          </samp>}\n          +<span class=sf-dump-public title=\"Public property\">created_at</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743874\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743874</span></span> {<a class=sf-dump-ref>#4396</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n            #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000112c0000000000000000</span>\"\n            -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n            <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:44:35.078347 from now\nDST Off\">2025-09-01 16:24:34.900614 UTC (+00:00)</span>\n          </samp>}\n        </samp>}\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    </samp>}\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1756743840\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1756743840</span></span> {<a class=sf-dump-ref>#4397</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000112d0000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Monday, September 1, 2025\n- 00:45:09.692973 from now\nDST Off\">2025-09-01 16:24:00.285438 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ac3ad28e5760b4699bfcd47a50c6957d</span>\"\n  \"<span class=sf-dump-key>07df26edabf1a8846957c43ccff73a8c</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682773919\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://adawliahshop.gc/admin/ecommerce/branch-inventory", "action_name": "ecommerce.branch-inventory.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\BranchInventoryController@index"}, "badge": null}}