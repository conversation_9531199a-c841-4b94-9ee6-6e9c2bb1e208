@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">{{ trans('Branch Inventory Management') }}</h4>
                    <div class="card-header-actions">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bulkUpdateModal">
                            <i class="fa fa-upload"></i> {{ trans('Bulk Update') }}
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#transferModal">
                            <i class="fa fa-exchange-alt"></i> {{ trans('Transfer Stock') }}
                        </button>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#initializeModal">
                            <i class="fa fa-plus"></i> {{ trans('Initialize Product') }}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="branch-filter">{{ trans('Filter by Branch') }}</label>
                            <select id="branch-filter" class="form-control">
                                <option value="">{{ trans('All Branches') }}</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="product-filter">{{ trans('Filter by Product') }}</label>
                            <select id="product-filter" class="form-control">
                                <option value="">{{ trans('All Products') }}</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}">{{ $product->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="status-filter">{{ trans('Filter by Status') }}</label>
                            <select id="status-filter" class="form-control">
                                <option value="">{{ trans('All Status') }}</option>
                                <option value="in_stock">{{ trans('In Stock') }}</option>
                                <option value="low_stock">{{ trans('Low Stock') }}</option>
                                <option value="out_of_stock">{{ trans('Out of Stock') }}</option>
                            </select>
                        </div>
                    </div>

                    {!! $dataTable->renderTable() !!}
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Update Modal -->
    <div class="modal fade" id="bulkUpdateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ trans('Bulk Update Stock') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="bulk-update-form">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="bulk-branch">{{ trans('Select Branch') }}</label>
                            <select id="bulk-branch" name="branch_id" class="form-control" required>
                                <option value="">{{ trans('Choose Branch') }}</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label>{{ trans('Upload CSV File') }}</label>
                            <input type="file" id="csv-file" class="form-control" accept=".csv">
                            <small class="text-muted">{{ trans('CSV format: product_id,quantity') }}</small>
                        </div>
                        <div id="bulk-products-container" style="display: none;">
                            <h6>{{ trans('Products to Update') }}</h6>
                            <div id="bulk-products-list"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('Cancel') }}</button>
                        <button type="submit" class="btn btn-primary">{{ trans('Update Stock') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Transfer Stock Modal -->
    <div class="modal fade" id="transferModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ trans('Transfer Stock') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="transfer-form">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="from-branch">{{ trans('From Branch') }}</label>
                            <select id="from-branch" name="from_branch_id" class="form-control" required>
                                <option value="">{{ trans('Choose Branch') }}</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="to-branch">{{ trans('To Branch') }}</label>
                            <select id="to-branch" name="to_branch_id" class="form-control" required>
                                <option value="">{{ trans('Choose Branch') }}</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="transfer-product">{{ trans('Product') }}</label>
                            <select id="transfer-product" name="product_id" class="form-control" required>
                                <option value="">{{ trans('Choose Product') }}</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}">{{ $product->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="transfer-quantity">{{ trans('Quantity') }}</label>
                            <input type="number" id="transfer-quantity" name="quantity" class="form-control" min="1" required>
                        </div>
                        <div id="available-stock" class="alert alert-info" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('Cancel') }}</button>
                        <button type="submit" class="btn btn-primary">{{ trans('Transfer') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Initialize Product Modal -->
    <div class="modal fade" id="initializeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ trans('Initialize Product Inventory') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="initialize-form">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="init-product">{{ trans('Product') }}</label>
                            <select id="init-product" name="product_id" class="form-control" required>
                                <option value="">{{ trans('Choose Product') }}</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}">{{ $product->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="default-quantity">{{ trans('Default Quantity') }}</label>
                            <input type="number" id="default-quantity" name="default_quantity" class="form-control" min="0" value="0">
                            <small class="text-muted">{{ trans('This quantity will be set for all branches') }}</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('Cancel') }}</button>
                        <button type="submit" class="btn btn-success">{{ trans('Initialize') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('footer')
    {!! $dataTable->renderScripts() !!}
@endpush
