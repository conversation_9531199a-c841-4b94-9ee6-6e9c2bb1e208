<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('ec_branch_inventory', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('ec_products')->onDelete('cascade');
            $table->integer('quantity')->unsigned()->default(0);
            $table->integer('min_stock_alert')->unsigned()->default(5);
            $table->timestamps();

            // Indexes for performance
            $table->index(['branch_id', 'product_id']);
            $table->index(['product_id', 'quantity']);
            $table->index(['branch_id', 'quantity']);
            $table->index(['quantity', 'min_stock_alert']);
            
            // Unique constraint to prevent duplicate entries
            $table->unique(['branch_id', 'product_id'], 'branch_product_unique');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ec_branch_inventory');
    }
};
