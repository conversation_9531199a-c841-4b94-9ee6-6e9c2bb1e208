<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON>ble\Ecommerce\Models\BranchInventory;
use Botble\Ecommerce\Models\Product;
use Bo<PERSON>ble\Ecommerce\Services\BranchInventoryService;
use Bo<PERSON>ble\Ecommerce\Tables\BranchInventoryTable;
use Botble\BranchManagement\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BranchInventoryController extends BaseController
{
    public function __construct(protected BranchInventoryService $branchInventoryService)
    {
    }

    public function index(BranchInventoryTable $dataTable)
    {
        $this->pageTitle(trans('Branch Inventory Management'));

        Assets::addScriptsDirectly('vendor/core/plugins/ecommerce/js/branch-inventory.js');

        $branches = Branch::active()->orderBy('name')->get();
        $products = Product::wherePublished()->orderBy('name')->get();

        return $dataTable->renderTable([
            'branches' => $branches,
            'products' => $products,
        ]);
    }

    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'product_id' => 'required|exists:ec_products,id',
            'quantity' => 'required|integer|min:0',
        ]);

        try {
            $this->branchInventoryService->setStock(
                $request->input('branch_id'),
                $request->input('product_id'),
                $request->input('quantity')
            );

            return $this->httpResponse()->withUpdatedSuccessMessage();
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage($e->getMessage());
        }
    }

    public function bulkUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'stock_data' => 'required|array',
            'stock_data.*' => 'integer|min:0',
        ]);

        try {
            $this->branchInventoryService->bulkUpdateStock(
                $request->input('branch_id'),
                $request->input('stock_data')
            );

            return $this->httpResponse()->withUpdatedSuccessMessage();
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage($e->getMessage());
        }
    }

    public function transfer(Request $request): JsonResponse
    {
        $request->validate([
            'from_branch_id' => 'required|exists:branches,id',
            'to_branch_id' => 'required|exists:branches,id|different:from_branch_id',
            'product_id' => 'required|exists:ec_products,id',
            'quantity' => 'required|integer|min:1',
        ]);

        try {
            $this->branchInventoryService->transferStock(
                $request->input('from_branch_id'),
                $request->input('to_branch_id'),
                $request->input('product_id'),
                $request->input('quantity')
            );

            return $this->httpResponse()->withUpdatedSuccessMessage();
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage($e->getMessage());
        }
    }

    public function getProductStock(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:ec_products,id',
        ]);

        $stockData = $this->branchInventoryService->getProductStockAcrossBranches(
            $request->input('product_id')
        );

        return $this->httpResponse()->setData($stockData);
    }

    public function getBranchStock(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
        ]);

        $branchId = $request->input('branch_id');
        
        $lowStock = $this->branchInventoryService->getLowStockItems($branchId);
        $outOfStock = $this->branchInventoryService->getOutOfStockItems($branchId);

        return $this->httpResponse()->setData([
            'low_stock' => $lowStock,
            'out_of_stock' => $outOfStock,
        ]);
    }

    public function initializeProduct(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:ec_products,id',
            'default_quantity' => 'integer|min:0',
        ]);

        try {
            $this->branchInventoryService->initializeProductInventory(
                $request->input('product_id'),
                $request->input('default_quantity', 0)
            );

            return $this->httpResponse()->withCreatedSuccessMessage();
        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage($e->getMessage());
        }
    }
}
