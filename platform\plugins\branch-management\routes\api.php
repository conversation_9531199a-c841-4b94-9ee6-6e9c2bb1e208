<?php

use Botble\BranchManagement\Http\Controllers\Api\BranchProductController;
use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api'],
    'prefix' => 'api/v1',
], function () {
    Route::group(['prefix' => 'branches'], function () {
        Route::get('/by-city', [BranchProductController::class, 'getBranchesByCity']);
        Route::get('/product-stock', [BranchProductController::class, 'getProductStock']);
        Route::get('/check-availability', [BranchProductController::class, 'checkAvailability']);
    });
});
