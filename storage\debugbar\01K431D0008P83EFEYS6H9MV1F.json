{"__meta": {"id": "01K431D0008P83EFEYS6H9MV1F", "datetime": "2025-09-01 16:22:41", "utime": **********.921205, "method": "GET", "uri": "/admin/ecommerce/products/product-attribute-sets", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": 1756743760.881872, "end": **********.92123, "duration": 1.039358139038086, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1756743760.881872, "relative_start": 0, "end": **********.839778, "relative_end": **********.839778, "duration": 0.****************, "duration_str": "958ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.839788, "relative_start": 0.****************, "end": **********.921234, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "81.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.857296, "relative_start": 0.**************, "end": **********.867335, "relative_end": **********.867335, "duration": 0.010039091110229492, "duration_str": "10.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.908394, "relative_start": 1.***************, "end": **********.918303, "relative_end": **********.918303, "duration": 0.009908914566040039, "duration_str": "9.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "adawliahshop.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00244, "accumulated_duration_str": "2.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}], "start": **********.8846319, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 0, "width_percent": 16.803}, {"sql": "select exists(select * from `activations` where `activations`.`user_id` = 1 and `activations`.`user_id` is not null and `completed` = 1) as `exists`", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.8888988, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "User.php:122", "source": {"index": 14, "namespace": null, "name": "vendor/botble/platform/acl/src/Models/User.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\acl\\src\\Models\\User.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=122", "ajax": false, "filename": "User.php", "line": "122"}, "connection": "adawliahshop", "explain": null, "start_percent": 16.803, "width_percent": 17.623}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.8947651, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 34.426, "width_percent": 32.787}, {"sql": "select `ec_product_attribute_sets`.* from `ec_product_attribute_sets` where `status` = 'published' order by `ec_product_attribute_sets`.`order` asc, `ec_product_attribute_sets`.`created_at` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductAttributeSet.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Models\\ProductAttributeSet.php", "line": 105}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Traits/ProductActionsTrait.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Traits\\ProductActionsTrait.php", "line": 805}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.903286, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 67.213, "width_percent": 19.672}, {"sql": "select `id`, `slug`, `title`, `attribute_set_id` from `ec_product_attributes` where `ec_product_attributes`.`attribute_set_id` in (1, 2) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductAttributeSet.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Models\\ProductAttributeSet.php", "line": 105}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Traits/ProductActionsTrait.php", "file": "D:\\laragon\\www\\adawliahshop\\platform\\plugins\\ecommerce\\src\\Traits\\ProductActionsTrait.php", "line": 805}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.905345, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\adawliahshop\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "adawliahshop", "explain": null, "start_percent": 86.885, "width_percent": 13.115}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\ProductAttribute": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductAttribute.php&line=1", "ajax": false, "filename": "ProductAttribute.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductAttributeSet": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductAttributeSet.php&line=1", "ajax": false, "filename": "ProductAttributeSet.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 14, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 14}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://adawliahshop.gc/admin/ecommerce/products/product-attribute-sets", "action_name": "products.product-attribute-sets", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@getProductAttributeSets", "uri": "GET admin/ecommerce/products/product-attribute-sets/{id?}", "permission": "products.index", "controller": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@getProductAttributeSets<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTraits%2FProductActionsTrait.php&line=797\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fadawliahshop%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FTraits%2FProductActionsTrait.php&line=797\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Traits/ProductActionsTrait.php:797-810</a>", "middleware": "web, core, auth", "duration": "1.04s", "peak_memory": "68MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1352523328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1352523328\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1415879492 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1415879492\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-979798116 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">adawliahshop.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">https://adawliahshop.gc/admin/ecommerce/products/edit/87</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2662 characters\">botble_footprints_cookie=eyJpdiI6InBYMHJNeEtkbHFlbWpBZU5QVnQrYlE9PSIsInZhbHVlIjoiM0JxSk5CTXlleEZZMzNCb3h5cUdTVXBBajFKTVI0VFZxemc3eWtvNWdoZHF2bUdRbkZUM2hscVkvcWk1Q2RJRVRTVDBGNmZjVEpZNFozWFU4K0RsL09Fby9BZGx3NGRHdjdwbDFZWDg4YmJZUkpmSmJXa0EwSlRJUWhmTEdqY3UiLCJtYWMiOiI3ZDRiNDUwNGFkYzNlOWZhYTNkNTNkOTlkYTMwYTkxY2U0MTAxMzJlZDFlMzNjOGJjZTZjYWFlMGI2ZGRmMzNiIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IjBNcGkyazd4dExDbnlaSmNmUnpOd0E9PSIsInZhbHVlIjoiMUtFOGhLckFUdnpGWVRyekl0MVpCVTZSKytUYitPNk5LR3oxTUdTbDdHZUt5MmtWS1ZJV2d0NFNNY1FDeWhnenJ4VDhRQk9BMDRqNklkZXU5UlNHZ1dBdStUUThWZHl2dkJzUDh2V09HMDRwa0NCY0NsTmt3NGV4MVlBWmhmRDUvbnpKOGZXcnU4aHJMWjZqVnB3akdLWGxsWmlHNVpSSVFhUFIvbTM2UE5IY01IVDhUMTlLN3lST1RoWW1iYVFQeTdpdjBsYjZrUTl2eE5oSnJLMU8rZUFqRjVDaVZUYTRBamxBM3phMUd2SlZTOGVyNXh0NzNxeVdXdmU0N0pLZGU0OVJvSUU4YXg4ZmM3dDVsMkRwSkx4RjN1NWN4aW1QbS9QSGJLdjlUdktvRW1UR1BIT3hqYUZSRy84YnZGQmxRT3VpZ2JsSGlGZDM2MmF3KzdzMms2VEJ0UVhZSE0vTzZKWU5pQ3JKYUhhbXhKc0dxK056dWZHTkp3QlZ6bW1LRFpxcmlkeGpXMHpoNG5uS0hkSitQWlhPTnJWb1Z4TDZXcDJFNmNFVTZjcW5DNmMwQUd3QWhoUVMySGpIZUkrRWxlamNVOC9KYzl1ZlN6aWUrSTdHWlZGYVorZGkwVFo4Nys1UG5IcDdFcjBYSmE3anRRQXhGcWdZNzlQQWpCVTFpZDh3RHJMaG1EZHRkVXpoUFVUNlhnPT0iLCJtYWMiOiIxZTdkY2JiZTI5NWNmMjdkZGQ5YThhYjdlYmUwMzk4NWZjZDkzYzJiNmYwZDFhMmE5ZDA3NDdiZmQzY2E0MjBhIiwidGFnIjoiIn0%3D; perf_dv6Tr4n=1; botble_cookie_newsletter=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkpIaFpaMEp5SFBNQ0pmZisvZStlaFE9PSIsInZhbHVlIjoiVXJHbWNMUmptbk5paVZvK0ZHUHNDaDFVQWp3L1NOMEJVdWJnWVFKWHhHWWJ5ZlF5TkpZRE1sWGRBRk85bUtUek9xVUc0cHV3cEkzYUVLbzgzQTVvUUJwV2NBaGQ0R3ROUG9xN3hodUxicG9XUS9OZlE1dVVIRXBQaDg4REF4NEpHWjhrZ0Z4Mk9iV293d1JDd0p0YXR4ejNDZTdCNEc3SGRSUWpUQ3JLUmtlcUx5S250TWI3ajY5SkFqRFgvOFMxY3RRVjFQdEtKR3ppVGRnaVg2dVpKUXdWZ0NzVndkdDVqZU9la2FGdmFDcz0iLCJtYWMiOiJmMjllOGFmY2RjOGE0OTQ2ZjdjMjdlMDVmMjFhZWY5ZDA2NTcxNTkzNDA4NmU1OGNlZWNlZjJkM2VkOGE4M2U0IiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; XSRF-TOKEN=eyJpdiI6Ild0NXlNd0JIM0tLVkYwRkxSbGlJN1E9PSIsInZhbHVlIjoiamIxTVhzcjMrWjlpWkthaE0xNXliTENDSkJMbDhrZGJxZnI3T1hFaDVBQzFUYlRGa0ZpNzI5ZjNNR2RsM20rNU5UUG16TndVVXZtTVh6ek5YanVyUnVFSExDbDh0YlFuSUNqR213M3lsUWJIYlhlQkI0akZwS05Ub0JRb3RpVmoiLCJtYWMiOiJiNzZmNjcxOTMxNjQyNmM0NGZiYjJmZmRiZDM1Y2E1MGNmMzJlMDkwNzJiM2JmZDkzYWUxMTUyZGY5ZTI0YjFlIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6Ijl1emNhQ3pVZzAxVnRSVmptNndOWXc9PSIsInZhbHVlIjoiTjNObk9BNDk1NWJCcVJzRFUrM3NNSDM1UmRtM2RJY09VYUxRYTJrUzBCaHBVbjhWM0NLVEJOUXRxRG1pUUVqdVBlY0lDRC9aM1UwSHlqNjRrMEZSb2d5TEZlZitVT2t1R2hmNnNZckF6WVV2ZjlxVURnN0g2TnVRSjUwMnB1OVYiLCJtYWMiOiIxZjE2ZDNkYmNiZjE5NmM1ZmNjNGMzMWMwM2ViNWFhOTk0Yjk0ZTk1Y2QyMzgzNTdhYTUwMzczZmU3ZjU3YzA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979798116\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-36696211 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">b611e49ca9c9e01d12f55b9fdbec097ba769ddeb</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"351 characters\">{&quot;footprint&quot;:&quot;b611e49ca9c9e01d12f55b9fdbec097ba769ddeb&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;adawliahshop.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yOi7kX3uphlBCTLAiO3znuue74DN3GeKZVAdyGvz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36696211\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-286779119 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 01 Sep 2025 16:22:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286779119\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-405612745 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xW2GRq2BwphAv7t7qygJeO2hrc2kvNjTEGLj1eVO</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>5</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>+</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://adawliahshop.gc/admin/ecommerce/products/edit/87</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405612745\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://adawliahshop.gc/admin/ecommerce/products/product-attribute-sets", "action_name": "products.product-attribute-sets", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@getProductAttributeSets"}, "badge": null}}